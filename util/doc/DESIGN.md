# Util Module Design Document

This document provides a comprehensive overview of the `util` module within the Genesis2 project, detailing its architecture, components, and integration patterns.

## Module Overview

The `util` module serves as the foundational utility library for the Genesis2 project, providing core functionality that is shared across all other modules. It acts as the base dependency for common operations including:

- **Unique Identifier Generation**: UUID-based identifier management
- **JSON Processing**: Custom JSON parsing and manipulation framework
- **Design Patterns**: Common patterns for data handling and object management
- **Utility Classes**: Helper classes for set operations and data validation

### Scope and Purpose

- **Foundation Layer**: Provides essential utilities used by all Genesis2 modules
- **Cross-Module Integration**: Enables consistent data handling across the project
- **Performance Optimization**: Implements efficient algorithms for common operations
- **Type Safety**: Ensures robust data processing with proper error handling

## Architecture

The util module is organized into several key packages, each with specific responsibilities:

```
util/
├── src/main/java/
│   ├── uniqueid/           # Unique identifier generation
│   ├── json/               # JSON processing framework
│   │   ├── parser/         # JSON parsing utilities
│   │   ├── rep/            # JSON representation classes
│   │   └── schema/         # JSON schema handling
│   ├── patterns/           # Design patterns and utilities
│   │   ├── dataissues/     # Data validation patterns
│   │   ├── comparable/     # Comparison utilities
│   │   └── copyObj/        # Object copying patterns
│   └── set/                # Set operations and utilities
└── doc/                    # Documentation
```

### Package Responsibilities

| Package | Purpose | Key Classes |
|---------|---------|-------------|
| `uniqueid` | UUID generation and management | `GGuid` |
| `json.parser` | JSON string parsing | `JSONGENParser` |
| `json.rep` | JSON object representation | `JSONGENNode`, `JSONGENObj` |
| `json.schema` | JSON schema validation | `JSONGENSchema`, `JSONGENSchemaParser` |
| `patterns.dataissues` | Data validation patterns | `IDataIssues` |
| `patterns.comparable` | Object comparison utilities | TBD |
| `patterns.copyObj` | Object copying patterns | TBD |
| `set` | Set operations | `IsInSet` |

## Core Components

### Unique Identifier Generation

#### GGuid Class

The `GGuid` class provides UUID-based unique identifier generation with Genesis2-specific enhancements:

```java
// Generate a new unique identifier
GGuid newId = GGuid.GenerateGuid();

// Create from existing UUID string
GGuid existingId = new GGuid("93cbe488-1504-48da-85c8-78ef40c4cde9");

// Convert to string representation
String idString = newId.toString();

// Get fixed write size for serialization
long writeSize = GGuid.getFixedWriteSize();
```

**Key Features:**
- Thread-safe UUID generation
- String-based constructor for deserialization
- Fixed-size serialization support
- Class-level identifiers for metadata

### JSON Processing Framework

#### JSONGENParser

The main entry point for JSON parsing operations:

```java
// Parse JSON string to JSONGENNode
JSONGENNode node = JSONGENParser.parseString(jsonString);

// Parse with schema validation
JSONGENNode validatedNode = JSONGENParser.parseString(jsonString, schema);

// Automatic schema detection
JSONGENNode schemaNode = JSONGENParser.parseString(schemaJsonString);
```

**Features:**
- Automatic schema detection and parsing
- Schema-validated parsing
- Integration with Gson JsonElement
- Support for both schema and data documents

#### JSONGENNode and JSONGENObj

Core classes for JSON object representation and manipulation:

```java
// Create JSON object from JsonElement
JSONGENNode node = JSONGENNode.createObject(jsonElement);

// Create with schema validation
JSONGENNode validatedNode = JSONGENNode.createObject(jsonElement, schema);

// Access JSON object functionality
JSONGENObj jsonObj = (JSONGENObj) node; // if node is an object
```

**Capabilities:**
- Type-safe JSON object access
- Schema-aware validation
- Efficient memory representation
- Integration with Gson parsing

### Design Patterns

#### Data Issues Pattern

The `IDataIssues` interface defines a contract for data validation and issue reporting:

```java
public interface IDataIssues {
    // Implementation classes should provide:
    // - Data validation methods
    // - Issue collection and reporting
    // - Error handling strategies
}
```

#### Set Operations

The `IsInSet` utility class provides efficient set membership testing:

```java
// Example usage (implementation details to be documented)
boolean isMember = IsInSet.contains(collection, item);
```

## Dependencies

The util module has carefully selected dependencies to minimize external requirements while providing essential functionality:

### External Dependencies

```xml
<!-- Google Gson for JSON processing -->
<dependency>
    <groupId>com.google.code.gson</groupId>
    <artifactId>gson</artifactId>
    <version>2.10.1</version>
</dependency>

<!-- Apache Commons IO for file operations -->
<dependency>
    <groupId>commons-io</groupId>
    <artifactId>commons-io</artifactId>
    <version>2.11.0</version>
</dependency>

<!-- JUnit Jupiter for testing -->
<dependency>
    <groupId>org.junit.jupiter</groupId>
    <artifactId>junit-jupiter</artifactId>
    <version>5.8.2</version>
    <scope>test</scope>
</dependency>
```

### Dependency Rationale

- **Gson 2.10.1**: Provides robust JSON parsing and serialization capabilities
- **Commons IO 2.11.0**: Offers reliable file I/O operations and utilities
- **JUnit 5.8.2**: Enables comprehensive unit testing with modern testing features

## Usage Examples

### Basic JSON Processing

```java
import json.parser.JSONGENParser;
import json.rep.JSONGENNode;

// Parse a JSON string
String jsonData = "{\"name\":\"John\",\"age\":30,\"active\":true}";
JSONGENNode node = JSONGENParser.parseString(jsonData);

// Work with the parsed data
// (Specific API methods depend on JSONGENNode implementation)
```

### Unique Identifier Management

```java
import uniqueid.GGuid;

// Generate new identifiers
GGuid userId = GGuid.GenerateGuid();
GGuid sessionId = GGuid.GenerateGuid();

// Use in data structures
Map<GGuid, UserData> userMap = new HashMap<>();
userMap.put(userId, new UserData());

// Serialize for storage
String userIdString = userId.toString();
```

### Schema-Validated JSON Processing

```java
import json.parser.JSONGENParser;
import json.schema.JSONGENSchema;

// Load schema
JSONGENSchema schema = loadSchema("user-schema.json");

// Parse and validate data
String userData = "{\"name\":\"Jane\",\"email\":\"<EMAIL>\"}";
JSONGENNode validatedUser = JSONGENParser.parseString(userData, schema);
```

## Integration

The util module serves as the foundation for other Genesis2 modules:

### Module Dependencies

```
util (foundation)
├── persistence → depends on util
├── gjson → depends on util  
├── thirdparty → depends on util
└── restserver2 → depends on util (via other modules)
```

### Integration Patterns

#### With Persistence Module
- Provides `GGuid` for entity identifiers
- Supplies JSON processing for data serialization
- Offers validation patterns for data integrity

#### With GJSON Module
- Shares JSON processing infrastructure
- Provides common utilities for JSON manipulation
- Enables consistent JSON handling across modules

#### With Thirdparty Modules
- Supplies base utilities for third-party integrations
- Provides common interfaces for data processing
- Ensures consistent error handling patterns

### Cross-Module Usage

```java
// Example: Using util in persistence layer
import uniqueid.GGuid;
import json.parser.JSONGENParser;

public class EntityManager {
    public Entity createEntity(String jsonData) {
        GGuid entityId = GGuid.GenerateGuid();
        JSONGENNode data = JSONGENParser.parseString(jsonData);
        return new Entity(entityId, data);
    }
}
```

## Development Guidelines

### Coding Standards

#### Package Organization
- Keep packages focused on single responsibilities
- Use clear, descriptive package names
- Maintain consistent naming conventions across packages

#### Class Design
- Prefer immutable objects where possible
- Implement proper equals() and hashCode() methods
- Use builder patterns for complex object construction
- Follow the single responsibility principle

#### Error Handling
- Use checked exceptions for recoverable errors
- Provide meaningful error messages
- Implement proper resource cleanup
- Log errors at appropriate levels

### Best Practices

#### JSON Processing
```java
// Good: Use try-with-resources for proper cleanup
try {
    JSONGENNode node = JSONGENParser.parseString(jsonString);
    // Process node
} catch (JsonParseException e) {
    logger.error("Failed to parse JSON: {}", e.getMessage());
    throw new DataProcessingException("Invalid JSON format", e);
}
```

#### Unique Identifier Usage
```java
// Good: Use GGuid consistently
public class DataRecord {
    private final GGuid id;
    private final String data;
    
    public DataRecord(String data) {
        this.id = GGuid.GenerateGuid();
        this.data = data;
    }
}
```

#### Testing Guidelines
- Write unit tests for all public methods
- Use descriptive test method names
- Test both success and failure scenarios
- Mock external dependencies appropriately

```java
@Test
@DisplayName("Should generate unique GGuid instances")
void shouldGenerateUniqueGuids() {
    GGuid guid1 = GGuid.GenerateGuid();
    GGuid guid2 = GGuid.GenerateGuid();
    
    assertNotNull(guid1);
    assertNotNull(guid2);
    assertNotEquals(guid1.toString(), guid2.toString());
}
```

### Performance Considerations

- **Memory Management**: Reuse objects where appropriate, especially for frequently created instances
- **JSON Processing**: Cache parsed schemas for repeated validation
- **UUID Generation**: Consider pooling for high-frequency generation scenarios
- **String Operations**: Use StringBuilder for complex string construction

### Documentation Requirements

- Document all public APIs with Javadoc
- Include usage examples in class documentation
- Maintain this design document with architectural changes
- Update README files when adding new functionality

## Version Information

- **Java Version**: 21 (source and target)
- **Maven Version**: 3.6+
- **Encoding**: UTF-8
- **Last Updated**: January 2025

## Future Enhancements

### Planned Features
1. **Enhanced JSON Schema Support**: More comprehensive schema validation
2. **Performance Optimizations**: Caching and pooling for frequently used objects
3. **Additional Utility Classes**: More helper classes for common operations
4. **Improved Error Handling**: More specific exception types and better error messages

### Extension Points
- **Custom JSON Processors**: Plugin architecture for specialized JSON handling
- **Additional ID Formats**: Support for other identifier formats beyond UUID
- **Validation Framework**: Extensible validation system for different data types
- **Serialization Support**: Enhanced serialization capabilities for persistence

## Resources

- [Gson Documentation](https://github.com/google/gson)
- [Apache Commons IO](https://commons.apache.org/proper/commons-io/)
- [JUnit 5 User Guide](https://junit.org/junit5/docs/current/user-guide/)
- [Java UUID Documentation](https://docs.oracle.com/en/java/javase/21/docs/api/java.base/java/util/UUID.html)