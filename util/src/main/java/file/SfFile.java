package file;

import file.name.SfFileName;

import java.io.File;

/**
 * Represents a file within the SnapFS file system framework.
 * 
 * This class provides a cross-platform abstraction for file operations,
 * combining a directory location with a filename to represent a complete
 * file path. It offers methods to check file existence, create new files,
 * and retrieve the underlying Java File object.
 * 
 * The class uses SfDirectory for directory representation and SfFileName
 * for filename validation, ensuring proper handling across different
 * operating systems (Windows, Unix/Linux).
 */
public class SfFile {
    private SfDirectory directory;
    private SfFileName fileName;

    public SfFile(SfDirectory directory, SfFileName fileName) {
        this.fileName = fileName;        
        this.directory = directory;
    }

    public SfDirectory getDirectory() {
        return directory;
    }
    
    public SfFileName getFileName() {
        return fileName;
    }

    public boolean exists() {
        try {
            // Get the directory path
            String directoryPath = directory.getLocation().getPath();

            // Construct the full file path
            String fullFilePath = directoryPath + File.separator + fileName.getSFName();

            // Create a File object and check if it exists and is a file
            File file = new File(fullFilePath);
            return file.exists() && file.isFile();

        } catch (Exception e) {
            return false;
        }
    }

    public File create() {
        try {
            // Get the directory path
            String directoryPath = directory.getLocation().getPath();

            // Construct the full file path
            String fullFilePath = directoryPath + File.separator + fileName.getSFName();

            // Create a File object and check if it exists and is a file
            File file = new File(fullFilePath);
            file.createNewFile();
            return file;
        } catch (Exception e) {
            return null;
        }
    }

    public File getFile(){
        try {
            // Get the directory path
            String directoryPath = directory.getLocation().getPath();

            // Construct the full file path
            String fullFilePath = directoryPath + File.separator + fileName.getSFName();

            // Create a File object and check if it exists and is a file
            return new File(fullFilePath);
        } catch (Exception e) {
            return null;
        }
    }
}
