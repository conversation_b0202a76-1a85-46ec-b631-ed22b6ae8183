package file.name;

import javaHelpers.ASSERT;

public class SfDirectoryName extends SfName{
    public SfDirectoryName(String name) {
        this.name = name;
        if (isValidUnixName(name)){
            osType = OSTYPES.UNIX;
            if (isValidWinName(name)){
                osType = OSTYPES.ALL;
            }
        }else if (isValidWinName(name)){
            osType = OSTYPES.WINDOWS;
        }else{
            ASSERT.THROW("invalid name");
        }
    }

    public SfDirectoryName(SfDirectoryName name) {
        this.name = name.getSFName();
    }

    public static boolean isValidName(String name, OSTYPES type) {
        if (type == OSTYPES.ALL) {
            return isValidWinName(name) | isValidUnixName(name);
        } else if (type == OSTYPES.UNIX) {
            return isValidUnixName(name);
        } else if (type == OSTYPES.WINDOWS) {
            return isValidWinName(name);
        }
        return false;
    }

    
    /**
     * Validates whether a string is a valid Windows directory name.
     * 
     * Windows directory names have several restrictions:
     * - Cannot be null or empty
     * - Cannot use reserved names (CON, PRN, AUX, NUL, COM1-9, LPT1-9)
     * - Cannot contain invalid characters: < > : " | ? * / \
     * - Cannot contain control characters (ASCII < 32)
     * - Cannot end with space or period
     * 
     * Valid examples:
     * - "Documents"
     * - "My Folder"
     * - "Project_2024"
     * - "Data (Backup)"
     * - "temp-files"
     * - "New Folder"
     * 
     * Invalid examples:
     * - "CON" (reserved name)
     * - "folder<name>" (contains <)
     * - "directory:" (contains :)
     * - "folder " (ends with space)
     * - "name." (ends with period)
     * - "my|folder" (contains |)
     * - "data?" (contains ?)
     * 
     * @param name The directory name to validate
     * @return true if the name is a valid Windows directory name, false otherwise
     */
    private static boolean isValidWinName(String name) {
        if (name == null || name.isEmpty()) {
            return false;
        }
        
        // Check for reserved names
        String upperName = name.toUpperCase();
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                 "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                 "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
        
        for (String reserved : reservedNames) {
            if (upperName.equals(reserved) || upperName.startsWith(reserved + ".")) {
                return false;
            }
        }
        
        // Check for invalid characters
        char[] invalidChars = {'<', '>', ':', '"', '|', '?', '*', '/', '\\'};
        for (char c : name.toCharArray()) {
            if (c < 32) return false; // Control characters
            for (char invalid : invalidChars) {
                if (c == invalid) return false;
            }
        }
        
        // Check if name ends with space or period
        if (name.endsWith(" ") || name.endsWith(".")) {
            return false;
        }
        
        return true;
    }
    

    /**
     * Validates whether a string is a valid Unix directory name.
     * 
     * Unix directory names are generally permissive but have some restrictions:
     * - Cannot contain null characters (\0)
     * - Cannot contain forward slashes (/) as they are directory separators
     * - Cannot be "." or ".." (reserved for current and parent directory)
     * - Cannot be null or empty
     * 
     * Valid examples:
     * - "documents"
     * - "my-folder_2024"
     * - ".hidden_directory"
     * - "folder with spaces"
     * - "special!@#$%^&*()chars"
     * - "unicode文件夹"
     * 
     * Invalid examples:
     * - null or ""
     * - "path/to/folder" (contains /)
     * - "folder\0name" (contains null character)
     * - "." (current directory)
     * - ".." (parent directory)
     * 
     * @param name The directory name to validate
     * @return true if the name is a valid Unix directory name, false otherwise
     */
    public static boolean isValidUnixName(String name) {
        if (name == null || name.isEmpty()) {
            return false;
        }
        
        // Check for null character (not allowed in Unix directory names)
        if (name.contains("\0")) {
            return false;
        }
        
        // Check for forward slash (directory separator, not allowed in directory name)
        if (name.contains("/")) {
            return false;
        }
        
        // Check for reserved names
        if (name.equals(".") || name.equals("..")) {
            return false;
        }
        
        // Unix allows most characters, but some are problematic for shell usage
        // This is a more permissive validation - stricter rules could be applied
        return true;
    }
}