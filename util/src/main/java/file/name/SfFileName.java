package file.name;

import javaHelpers.ASSERT;

public class Sf<PERSON>ileName extends SfName{
    public SfFileName(String fileName) {
        this.name = fileName;
        if (isValidUnixName(fileName)){
            osType = OSTYPES.UNIX;
            if (isValidWinName(fileName)){
                osType = OSTYPES.ALL;
            }
        }else if (isValidWinName(fileName)){
            osType = OSTYPES.WINDOWS;
        }else{
            ASSERT.THROW("invalid name");
        }
    }

    public SfFileName(SfFileName name) {
        this.name = name.getSFName();
    }

    public static boolean isValidName(String name, OSTYPES type) {
        if (type == OSTYPES.ALL) {
            return isValidWinName(name) | isValidUnixName(name);
        } else if (type == SfName.OSTYPES.UNIX) {
            return isValidUnixName(name);
        } else if (type == SfName.OSTYPES.WINDOWS) {
            return isValidWinName(name);
        }
        return false;
    }
    
    
    /**
     * Validates whether a string is a valid Windows filename.
     * 
     * Windows filenames have several restrictions:
     * - Cannot be null or empty
     * - Cannot use reserved names (CON, PRN, AUX, NUL, COM1-9, LPT1-9)
     * - Cannot contain invalid characters: < > : " | ? * / \
     * - Cannot contain control characters (ASCII < 32)
     * - Cannot end with space or period
     * 
     * Valid examples:
     * - "document.txt"
     * - "my-file_2024.pdf"
     * - "Report (Final).docx"
     * - "data123.json"
     * 
     * Invalid examples:
     * - "CON" or "CON.txt" (reserved name)
     * - "file<name>.txt" (contains <)
     * - "document:" (contains :)
     * - "file " (ends with space)
     * - "name." (ends with period)
     * 
     * @param name The filename to validate
     * @return true if the name is a valid Windows filename, false otherwise
     */
    public static boolean isValidWinName(String name) {
        if (name == null || name.isEmpty()) {
            return false;
        }
        
        // Check for reserved names
        String upperName = name.toUpperCase();
        String[] reservedNames = {"CON", "PRN", "AUX", "NUL", "COM1", "COM2", "COM3", "COM4", 
                                 "COM5", "COM6", "COM7", "COM8", "COM9", "LPT1", "LPT2", 
                                 "LPT3", "LPT4", "LPT5", "LPT6", "LPT7", "LPT8", "LPT9"};
        
        for (String reserved : reservedNames) {
            if (upperName.equals(reserved) || upperName.startsWith(reserved + ".")) {
                return false;
            }
        }
        
        // Check for invalid characters
        char[] invalidChars = {'<', '>', ':', '"', '|', '?', '*', '/', '\\'};
        for (char c : name.toCharArray()) {
            if (c < 32) return false; // Control characters
            for (char invalid : invalidChars) {
                if (c == invalid) return false;
            }
        }
        
        // Check if name ends with space or period
        if (name.endsWith(" ") || name.endsWith(".")) {
            return false;
        }
        
        return true;
    }

    
    /**
     * Validates whether a string is a valid Unix filename.
     * 
     * Unix filenames are generally permissive but have some restrictions:
     * - Cannot contain null characters (\0)
     * - Cannot contain forward slashes (/) as they are directory separators
     * - Cannot be "." or ".." (reserved for current and parent directory)
     * - Cannot be null or empty
     * 
     * Valid examples:
     * - "document.txt"
     * - "my-file_2024.pdf"
     * - ".hidden_file"
     * - "file with spaces"
     * - "special!@#$%^&*()chars"
     * - "unicode文件名.txt"
     * 
     * Invalid examples:
     * - null or ""
     * - "path/to/file" (contains /)
     * - "file\0name" (contains null character)
     * - "." (current directory)
     * - ".." (parent directory)
     * 
     * @param name The filename to validate
     * @return true if the name is a valid Unix filename, false otherwise
     */
    public static boolean isValidUnixName(String name) {
        if (name == null || name.isEmpty()) {
            return false;
        }
        
        // Check for null character (not allowed in Unix filenames)
        if (name.contains("\0")) {
            return false;
        }
        
        // Check for forward slash (directory separator, not allowed in filename)
        if (name.contains("/")) {
            return false;
        }
        
        // Check for reserved names
        if (name.equals(".") || name.equals("..")) {
            return false;
        }
        
        // Unix allows most characters, but some are problematic for shell usage
        // This is a more permissive validation - stricter rules could be applied
        return true;
    }

}