package file.name;

import javaHelpers.ASSERT;
import os.CurrentOS;

public class SfVolumeName extends SfName{
    public SfVolumeName(String name) {
        this.name = name;
        if (isValidUnixName(name)){
            osType = OSTYPES.UNIX;
            if (isValidWinName(name)){
                osType = OSTYPES.ALL;
            }
        }else if (isValidWinName(name)){
            osType = OSTYPES.WINDOWS;
        }else{
            ASSERT.THROW("invalid name");
        }
    }

    public static boolean isValidName(String name, OSTYPES type ) {
        if (type == OSTYPES.ALL) {
            return isValidWinName(name) | isValidUnixName(name);
        } else if (type == OSTYPES.UNIX) {
            return isValidUnixName(name);
        } else if (type == OSTYPES.WINDOWS) {
            return isValidWinName(name);
        }
        return false;
    }

    public static boolean isValidName(String name) {
        CurrentOS.OSType osType = CurrentOS.getOperatingSystemType();
        if (osType == CurrentOS.OSType.Windows ){
            return isValidName(name,OSTYPES.WINDOWS);
        }else{
            return isValidName(name,OSTYPES.UNIX);
        }

    }

    /**
     * Validates whether a string is a valid Windows volume name.
     * 
     * Windows volume names are drive letters followed by a colon:
     * - Must be exactly 2 characters long
     * - First character must be a letter (A-Z or a-z)
     * - Second character must be a colon (:)
     * 
     * Valid examples:
     * - "C:"
     * - "D:"
     * - "Z:"
     * - "a:"
     * - "x:"
     * 
     * Invalid examples:
     * - "C" (missing colon)
     * - "C:\" (too long)
     * - "1:" (starts with number)
     * - ":C" (wrong order)
     * - "CC:" (too many letters)
     * 
     * @param volName The volume name to validate
     * @return true if the name is a valid Windows volume name, false otherwise
     */
    private static boolean isValidWinName(String volName) {
        if (volName == null || volName.length() != 2) {
            return false;
        }

        if (volName.charAt(1) != ':') {
            return false;
        }
        
        char firstChar = volName.charAt(0);
        return (firstChar >= 'a' && firstChar <= 'z') || (firstChar >= 'A' && firstChar <= 'Z');
    }
    


    private static boolean isValidUnixName(String volName) {
        return SfDirectoryName.isValidUnixName(volName);
    }
}