package file.path;

import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;
import javax.swing.filechooser.FileSystemView;

import file.SfDirectory;
import file.name.SfName;
import file.name.SfVolumeName;
import file.name.SfDirectoryName;
import file.name.SfFileName;
import patterns.dumpable.IDumpable;
import ptype.types.PTStruct;
import thread.IMMutable;

public class SfLocation implements IDumpable, IMMutable {
    private final SfLocationNode root;

    public SfLocation(String path) throws IOException
    {
        Path folder = Paths.get(path);

        // Add all path components (skip root as it's handled separately)
        // Extract path elements from the folder path to build the location
        List<String> pathElements = new ArrayList<>();
        folder = folder.toAbsolutePath();
        for (int i = 0; i < folder.getNameCount(); i++) {
            pathElements.add(folder.getName(i).toString());
        }

        // Initialize the final root field directly
        this.root = buildLinkedList(pathElements, true);
    }

    public String getPath(){
        StringBuilder pathBuilder = new StringBuilder();
        SfLocationNode curNode = root;

        while (curNode != null) {
            pathBuilder.append(File.separator+curNode.getName().getSFName() );
            curNode = curNode.getChild();
        }
        return pathBuilder.toString();
    }

    /**
     * Returns a new SfLocation that represents a path relative to this location
     * by going up the specified number of directories.
     *
     * @param levelsUp The number of directory levels to go up
     * @return A new SfLocation representing the relative path
     * @throws IllegalArgumentException if levelsUp is negative or exceeds the path depth
     */
    public SfLocation getRelativeLocation(int levelsUp) {
        if (levelsUp < 0) {
            throw new IllegalArgumentException("levelsUp cannot be negative");
        }

        if (levelsUp == 0) {
            // Return a copy of this location
            return new SfLocation(copyNodeChain(this.root, getPathDepth()));
        }

        // Count the total number of components in the path by traversing down the chain
        int totalComponents = getPathDepth();

        if (levelsUp >= totalComponents) {
            throw new IllegalArgumentException("Cannot go up " + levelsUp +
                " levels from a path with only " + totalComponents + " components");
        }

        // Calculate how many components to keep
        int componentsToKeep = totalComponents - levelsUp;

        // Create a new root node chain up to the target
        return new SfLocation(copyNodeChain(root, componentsToKeep));
    }

    /**
     * Counts the depth of the path by traversing down the child chain
     */
    private int getPathDepth() {
        int count = 0;
        SfLocationNode current = root;
        while (current != null) {
            count++;
            current = current.getChild();
        }
        return count;
    }

    /**
     * Private constructor for creating SfLocation from an existing node chain
     */
    private SfLocation(SfLocationNode rootNode) {
        this.root = rootNode;
    }

    /**
     * Creates a copy of the node chain up to the specified number of components
     */
    private SfLocationNode copyNodeChain(SfLocationNode sourceRoot, int componentCount) {
        if (sourceRoot == null || componentCount <= 0) {
            return null;
        }

        SfLocationNode newRoot = new SfLocationNode(sourceRoot.getName());
        SfLocationNode currentNew = newRoot;
        SfLocationNode currentSource = sourceRoot.getChild();

        for (int i = 1; i < componentCount && currentSource != null; i++) {
            SfLocationNode newNode = new SfLocationNode(currentSource.getName());
            currentNew.setChild(newNode);
            currentNew = newNode;
            currentSource = currentSource.getChild();
        }

        return newRoot;
    }

    @Override
    public String dump(int detailLevel) {
        return getPath();
    }


    /**
     * Builds a linked list of SfLocationNodes from a list of path components
     * @param pathComponents List of string path components
     * @param isAbsolute Whether this is an absolute path (affects volume detection)
     * @return The root node of the linked list
     */
    private SfLocationNode buildLinkedList(List<String> pathComponents, boolean isAbsolute) {
        if (pathComponents == null || pathComponents.isEmpty()) {
            return null;
        }

        SfLocationNode rootNode = null;
        SfLocationNode currentNode = null;

        for (int i = 0; i < pathComponents.size(); i++) {
            String component = pathComponents.get(i);
            SfName name;

            // Determine the type of name based on position and content
            if (i == 0 && isAbsolute && SfVolumeName.isValidName(component)) {
                // First component of absolute path that looks like a volume
                name = new SfVolumeName(component);
            } else if (i == pathComponents.size() - 1 && false) {
                // Last component and this location represents a file
                name = new SfFileName(component);
            } else {
                // Directory component
                name = new SfDirectoryName(component);
            }

            // Create the node
            SfLocationNode newNode = new SfLocationNode(name);

            if (rootNode == null) {
                // First node becomes the root
                rootNode = newNode;
                currentNode = newNode;
            } else {
                // Link to the previous node
                currentNode.setChild(newNode);
                currentNode = newNode;
            }
        }

        return rootNode;
    }
}
