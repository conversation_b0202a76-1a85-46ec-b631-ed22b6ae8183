package file.path;

import file.name.SfName;
import patterns.dumpable.IDumpable;
import ptype.types.PTStruct;

import java.io.File;

public class SfLocationNode implements IDumpable {
    private SfLocationNode parent;
    private SfLocationNode child;
    private SfName name;

    public SfLocationNode(SfName name) {
        this.name = name;
        this.parent = null;
        this.child = null;
    }

    public SfLocationNode(SfName name, SfLocationNode parent) {
        this.name = name;
        this.parent = parent;
        this.child = null;
        if (parent != null) {
            parent.child = this;
        }
    }

    public SfName getName() {
        return name;
    }

    public SfLocationNode getParent() {
        return parent;
    }

    public SfLocationNode getChild() {
        return child;
    }

    public void setChild(SfLocationNode child) {
        this.child = child;
        if (child != null) {
            child.parent = this;
        }
    }

    public void setParent(SfLocationNode parent) {
        this.parent = parent;
        if (parent != null) {
            parent.child = this;
        }
    }

    public int getNumberPathComponent() {
        int count = 0;
        SfLocationNode current = this;
        while (current != null) {
            count++;
            current = current.getParent();
        }
        return count;
    }

    public SfLocationNode getPathComponent(int i) {
        SfLocationNode current = this;
        for (int j = 0; j < i; j++) {
            if (current == null) {
                return null;
            }
            current = current.getChild();
        }
        return current;
    }

    @Override
    public String dump(int detailLevel) {
        StringBuilder dump = new StringBuilder();
        dump.append("SfLocationNode: ");
        SfLocationNode curNode = this;
        while (curNode != null) {
            dump.append(curNode.getName().getSFName() + File.separator);
            curNode = curNode.getChild();
        }

        return dump.toString();
    }


}
