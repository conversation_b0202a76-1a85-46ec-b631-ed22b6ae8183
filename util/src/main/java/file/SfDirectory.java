package file;

import file.name.SfDirectoryName;
import file.name.SfFileName;
import file.path.SfLocation;
import patterns.dumpable.IDumpable;
import ptype.types.PTStruct;

import javax.swing.filechooser.FileSystemView;
import java.io.File;
import java.io.IOException;
import java.net.URISyntaxException;
import java.util.ArrayList;
import java.util.List;

public class SfDirectory implements IDumpable {
    private SfLocation location;
    private static SfDirectory exeLaunchLocation = null;
    private static SfDirectory tmpDirectory = null;
    private static SfDirectory executableLocation = null;
    private static SfDirectory programLocation = null;
    private static SfDirectory userHomeDirectory = null;
    private static SfDirectory desktopDirectory = null;

    public SfDirectory(String path) throws IOException {
        this.location = new SfLocation(path);
    }

    public SfDirectory(SfLocation location) {
        this.location = location;
    }

    public SfDirectory(SfDirectory directory) {
        location = directory.getLocation();
    }

    public SfLocation getLocation() {
        return location;
    }

    //ADD_HERE:  A method that returns a new directory relative to this one. Should just specify then number of directories to go up.  This will affect the SfLocation class as well. 
    // this function should not change this instance in any way.  It should return a new directory. 
    
    public boolean doesExist(){
        return new File(location.getPath()).exists();
    }

    public List<SfDirectoryName> getDirectories(){
        List<SfDirectoryName> directories = new ArrayList<>();

        try {
            File currentDir = new File(location.getPath());

            // Check if the current location exists and is a directory
            if (!currentDir.exists() || !currentDir.isDirectory()) {
                return directories; // Return empty list if not a valid directory
            }

            // List all files and directories in the current directory
            File[] files = currentDir.listFiles();

            if (files != null) {
                for (File file : files) {
                    // Only include directories, not files
                    if (file.isDirectory()) {
                        try {
                            // Create SfDirectoryName from the directory name
                            SfDirectoryName dirName = new SfDirectoryName(file.getName());
                            directories.add(dirName);
                        } catch (Exception e) {
                            // Skip directories with invalid names
                            // This could happen if the directory name doesn't meet the validation criteria
                            System.err.println("Skipping directory with invalid name: " + file.getName() + " - " + e.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error reading directories from: " + location.getPath() + " - " + e.getMessage());
        }

        return directories;
    }

    public List<SfFileName> getFiles(){
        List<SfFileName> files = new ArrayList<>();

        try {
            File currentDir = new File(location.getPath());

            // Check if the current location exists and is a directory
            if (!currentDir.exists() || !currentDir.isDirectory()) {
                return files; // Return empty list if not a valid directory
            }

            // List all files and directories in the current directory
            File[] fileEntries = currentDir.listFiles();

            if (fileEntries != null) {
                for (File file : fileEntries) {
                    // Only include files, not directories
                    if (file.isFile()) {
                        try {
                            // Create SfFileName from the file name
                            SfFileName fileName = new SfFileName(file.getName());
                            files.add(fileName);
                        } catch (Exception e) {
                            // Skip files with invalid names
                            // This could happen if the file name doesn't meet the validation criteria
                            System.err.println("Skipping file with invalid name: " + file.getName() + " - " + e.getMessage());
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.err.println("Error reading files from: " + location.getPath() + " - " + e.getMessage());
        }

        return files;
    }

    @Override
    public String dump(int detailLevel) {
        return location.dump(detailLevel);
    }


    public synchronized static SfDirectory getUserLocation(){
        if (exeLaunchLocation == null) {
            try {
                exeLaunchLocation = new SfDirectory(new SfLocation(System.getProperty("user.dir")));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return exeLaunchLocation;
    }

    public synchronized static SfDirectory getTmpLocation(){
        if (tmpDirectory == null) {
            try {
                tmpDirectory = new SfDirectory(new SfLocation(System.getProperty("java.io.tmpdir")));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return tmpDirectory;
    }

    public synchronized static SfDirectory getJavaHomeLocation(){
        if (executableLocation == null) {
            try {
                executableLocation = new SfDirectory(new SfLocation(System.getProperty("java.home")));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return executableLocation;
    }

    public synchronized static SfDirectory getProgramLocation(){
        if (programLocation == null) {
            try {
                // Get the location of the currently running program/JAR
                String programPath = SfLocation.class.getProtectionDomain()
                        .getCodeSource()
                        .getLocation()
                        .toURI()
                        .getPath();

                // If it's a JAR file, get the directory containing the JAR
                File programFile = new File(programPath);
                if (programFile.isFile()) {
                    programPath = programFile.getParent();
                }

                programLocation = new SfDirectory(new SfLocation(programPath));
            } catch (IOException | URISyntaxException e) {
                e.printStackTrace();
            }
        }
        return programLocation;
    }

    public synchronized static SfDirectory getUserHomeDirectory(){
        if (userHomeDirectory == null) {
            try {
                userHomeDirectory = new SfDirectory(new SfLocation(System.getProperty("user.home")));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return userHomeDirectory;
    }

    public synchronized static SfDirectory getDesktopDirectory(){
        if (desktopDirectory == null) {
            try {
                // Try to get desktop directory using FileSystemView first
                File desktopFile = FileSystemView.getFileSystemView().getHomeDirectory();
                String desktopPath = desktopFile.getAbsolutePath();

                // If FileSystemView doesn't give us the desktop, construct it manually
                if (!desktopFile.getName().equalsIgnoreCase("Desktop")) {
                    String userHome = System.getProperty("user.home");
                    desktopPath = userHome + File.separator + "Desktop";

                    // Check if the Desktop directory exists, if not, use user home as fallback
                    File desktopDir = new File(desktopPath);
                    if (!desktopDir.exists() || !desktopDir.isDirectory()) {
                        desktopPath = userHome;
                    }
                }

                desktopDirectory = new SfDirectory(new SfLocation(desktopPath));
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return desktopDirectory;
    }

}