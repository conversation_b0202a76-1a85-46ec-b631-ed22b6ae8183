package uniqueid;

import java.util.UUID;

public class GGuid
{

    public static GGuid clsId = new GGuid("93cbe488-1504-48da-85c8-78ef40c4cde9");
    public static String clsName = "GGuid";
    public static GGuid clsclsId = new GGuid("52667816-c7eb-4367-a713-199619028fb3");
    private final UUID m_uuid;

    public GGuid(){
        m_uuid = null;
    }
    public GGuid(UUID uuid)
    {
        m_uuid = uuid;
    }

    public GGuid(String guid)
    {
        m_uuid = UUID.fromString(guid);
    }

    public String toString()
    {
       return m_uuid.toString();
    }
    public static GGuid GenerateGuid(){
        return new GGuid(UUID.randomUUID());
    }

    public static long getFixedWriteSize(){
        return Long.BYTES + Long.BYTES;
    }

    public boolean compare(GGuid gClsId) {
        if (gClsId.m_uuid.getMostSignificantBits() != m_uuid.getMostSignificantBits()){
            return false;
        }
        return gClsId.m_uuid.getLeastSignificantBits() == m_uuid.getLeastSignificantBits();
    }

    @Override
    public int hashCode() {
        return m_uuid.hashCode();
    }

    @Override
    public boolean equals(Object obj) {
        if (super.equals(obj)){
            return true;
        }
        if (obj instanceof GGuid){
            return compare((GGuid)obj);
        }
        return false;
    }
}
