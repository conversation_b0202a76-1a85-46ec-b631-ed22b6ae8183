package buffer.mem.localheap;

import buffer.IBaseBufferRO;
import buffer.types.ByteCache;
import javaHelpers.ASSERT;
import javaHelpers.NumberHelpers;

public class LocalHeapBaseBufferRO implements IBaseBufferRO {

    protected byte[] buffer;

    public LocalHeapBaseBufferRO(long size) {
        if (size>Integer.MAX_VALUE){
            ASSERT.THROW("Buffer must be smaller than: " + Integer.MAX_VALUE);
        }
        buffer = new byte[(int)size];
    }

    @Override
    public long getBytesToLong(long posInBytes, byte numBytes) {
        ASSERT.IFTHROW(numBytes>Long.BYTES, "can only set number of bytes less size than a long");
        long retValue = 0;
        for(byte blcv=0;blcv< numBytes;++blcv){
            byte bcur = getByte(posInBytes+blcv);
            retValue = NumberHelpers.byteToLong(retValue,blcv,bcur);
        }
        return retValue;
    }

    @Override
    public byte getByte(long posInBytes) {
        ASSERT.IFTHROW(posInBytes >= getBufferSize(), "Setting past size of buffer pos: "+ posInBytes);
        return buffer[(int)posInBytes];

    }

    @Override
    public void getFillByteCache(long posInBytes, int numBytes, ByteCache cache) {

    }

    @Override
    public long getBufferSize() {
        return buffer.length;
    }
}
