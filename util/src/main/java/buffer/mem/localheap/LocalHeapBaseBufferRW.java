package buffer.mem.localheap;

import buffer.types.ByteCache;
import buffer.IBaseBufferRW;
import javaHelpers.ASSERT;
import javaHelpers.NumberHelpers;

public class LocalHeapBaseBufferRW extends LocalHeapBaseBufferRO implements IBaseBufferRW {
    public LocalHeapBaseBufferRW(long size) {
        super(size);
    }

    @Override
    public long setLongBytes(long posInBytes, byte numBytes, long bytes) {
        ASSERT.IFTHROW(numBytes>Long.BYTES, "can only set number of bytes less size than a long");

        for(short slcv = 0;slcv<numBytes;++slcv) {
            byte bytevalue = NumberHelpers.longToByte(bytes, slcv);
            setByte(posInBytes+slcv,bytevalue);
        }
        return posInBytes+numBytes;
    }

    @Override
    public long setByte(long posInBytes, byte bytes) {
        ASSERT.IFTHROW(posInBytes >= getBufferSize(), "Setting past size of buffer");
        buffer[(int)posInBytes] = bytes;
        return posInBytes+Byte.BYTES;
    }

    @Override
    public long setByteCache(long posInBytes, int numBytes, ByteCache cache) {
        ASSERT.IFTHROW(posInBytes+numBytes >= getBufferSize(), "Setting past size of buffer");
        for(short slcv=0;slcv < numBytes;++slcv){
            buffer[(int)posInBytes+slcv] = cache.getByte(slcv);
        }
        return posInBytes+numBytes;
    }
}
