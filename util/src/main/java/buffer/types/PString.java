package buffer.types;

import java.nio.charset.Charset;

public class PString {
    private String str;
    private Charset charSet;


    public PString (){
        this.str = "";
        setCharSet(Charset.defaultCharset());
    }

    public PString(ByteCache buf, Charset charSet) {
        this.str = new String(buf.getBuffer(),0,buf.getSize());
        if (charSet == null){
            setCharSet(Charset.defaultCharset());
        }else {
            setCharSet(charSet);
        }
    }

    public PString(String str,Charset charSet) {
        this.str = str;
        if (charSet == null){
            setCharSet(Charset.defaultCharset());
        }else {
            setCharSet(charSet);
        }
    }

    public void setFromString(String str){
        this.str = str;
    }

    public void clearAndSetBufSize(int size){
        this.str = null;
    }
    public void setFromByteCache(ByteCache bc){
        this.str = new String(bc.getBuffer(),0,bc.getSize());
    }

    public String getAsString(){
        return this.str;
    }
    public int getNumBytes() {
        return this.str.getBytes().length;
    }

    private void setCharSet(Charset charSet){
        if (charSet == null){
            this.charSet = Charset.defaultCharset();
        }
        this.charSet = charSet;
    }

    public byte[] getBytes() {
        return str.getBytes(this.charSet);
    }
}
