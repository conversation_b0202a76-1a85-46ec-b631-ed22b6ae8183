package buffer.types;

import javaHelpers.ASSERT;

public class ByteCache {
    private byte[] buffer;
    private int size;
    private boolean isSet;

    public ByteCache() {
        buffer = null;
        size = 0;
        isSet = false;
    }

    public ByteCache(byte[] buf){
        buffer = buf;
        size = buf.length;
        isSet = true;
    }

    public boolean isSet(){
        return isSet;
    }

    public void clear(){
        size = 0;
        isSet = false;
        //Arrays.fill( buffer, (byte) 0 );
    }

    public void set(){
        isSet = true;
    }

    public int setSize(int size){
        if (getCapacity() < size){
            buffer = new byte[size];
        }
        this.size = size;
        return size;
    }

    public int getSize(){
        return size;
    }

    public int getCapacity(){
        if (buffer == null){
            return 0;
        }
        return (short)buffer.length;
    }

    public byte setByte(int idx,byte value){
        ASSERT.IFTHROW(idx>= size,"Out of bounds");
        buffer[idx] = value;
        return value;
    }

    public byte getByte(int idx) {
        ASSERT.IFTHROW(idx>= size,"Out of bounds");
        return buffer[idx];
    }

    public byte[] getBuffer(){
        return buffer;
    }
}
