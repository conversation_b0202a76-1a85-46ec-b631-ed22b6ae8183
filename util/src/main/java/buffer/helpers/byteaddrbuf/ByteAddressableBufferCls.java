package buffer.helpers.byteaddrbuf;

public class ByteAddressableBufferCls {
    public static long getShortWriteSizeInBytes(){
        return Short.BYTES;
    }

    public static long getByteWriteSizeInBytes(){
        return Byte.BYTES;
    }

    public static long getIntWriteSizeInBytes(){
        return Integer.BYTES;
    }

    public static long getLongWriteSizeInBytes(){
        return Long.BYTES;
    }

    public static byte getDoubleWriteSizeInBytes(){
        return Double.BYTES;
    }

    public static long getFloatWriteSizeInBytes(){
        return Float.BYTES;
    }

    public static byte getBooleanWriteSizeInBytes(){
        return 1;
    }


}
