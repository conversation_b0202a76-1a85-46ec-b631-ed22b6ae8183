package buffer.helpers.byteaddrbuf;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.types.ByteCache;
import buffer.types.PString;
import javaHelpers.ASSERT;

public class ByteAddressableBuffer {
    public static long setBitsAsLong(IBaseBufferRW buf,long bytePos,long numBytes ,long value){
        return setBytesFromBuffer(buf,bytePos,(byte)numBytes,value);
    }

    public static long getBitsAsLong(IBaseBufferRO buf,long bytePos,long numBytes){
        return getBytesFromBuffer(buf,bytePos, (byte) numBytes);
    }

    public static long setLong(IBaseBufferRW buf,long bytePos,long value){
        return setBytesFromBuffer(buf,bytePos,(byte) ByteAddressableBufferCls.getLongWriteSizeInBytes(),value);
    }

    public static long getLong(IBaseBufferRO buf,long bytePos){
        return getBytesFromBuffer(buf,bytePos, (byte) ByteAddressableBufferCls.getLongWriteSizeInBytes());
    }

    public static long setInteger(IBaseBufferRW buf,long bytePos, int value){
        return setBytesFromBuffer(buf,bytePos, (byte)ByteAddressableBufferCls.getIntWriteSizeInBytes(),value);
    }

    public static int getInteger(IBaseBufferRO buf,long bytePos){
        return (int)getBytesFromBuffer(buf,bytePos, (byte) ByteAddressableBufferCls.getIntWriteSizeInBytes());
    }

    public static long setShort(IBaseBufferRW buf,long bytePos, short value){
        return setBytesFromBuffer(buf,bytePos, (byte)ByteAddressableBufferCls.getShortWriteSizeInBytes(),value);
    }

    public static short getShort(IBaseBufferRO buf,long bytePos){
        return (short)getBytesFromBuffer(buf,bytePos, (byte) ByteAddressableBufferCls.getShortWriteSizeInBytes());
    }

    public static long setByte(IBaseBufferRW buf,long bytePos, byte value){
        return setBytesFromBuffer(buf,bytePos, (byte)ByteAddressableBufferCls.getByteWriteSizeInBytes(),value);
    }

    public static byte getByte(IBaseBufferRO buf,long bytePos){
        return (byte)getBytesFromBuffer(buf,bytePos, (byte) ByteAddressableBufferCls.getByteWriteSizeInBytes());
    }

    public static long setFloat(IBaseBufferRW buf,long bytePos, float value){
        return setBytesFromBuffer(buf,bytePos, (byte)ByteAddressableBufferCls.getFloatWriteSizeInBytes(), (long) value);
    }

    public static float getFloat(IBaseBufferRO buf,long bytePos){
        return (float)getBytesFromBuffer(buf,bytePos, (byte) ByteAddressableBufferCls.getFloatWriteSizeInBytes());
    }
    public static long setDouble(IBaseBufferRW buf,long bytePos, double value){
        return setBytesFromBuffer(buf,bytePos, ByteAddressableBufferCls.getDoubleWriteSizeInBytes(), (long) value);
    }

    public static double getDouble(IBaseBufferRO buf,long bytePos){
        return (double)getBytesFromBuffer(buf,bytePos, ByteAddressableBufferCls.getDoubleWriteSizeInBytes());
    }

    public static long setBoolean(IBaseBufferRW buf,long bytePos, boolean value){
        if (value) {
            return setBytesFromBuffer(buf, bytePos, ByteAddressableBufferCls.getBooleanWriteSizeInBytes(), 1);
        }
        return setBytesFromBuffer(buf, bytePos, ByteAddressableBufferCls.getBooleanWriteSizeInBytes(), 0);
    }

    public static boolean getBoolean(IBaseBufferRO buf,long bytePos){
        long value = getBytesFromBuffer(buf,bytePos, ByteAddressableBufferCls.getBooleanWriteSizeInBytes());
        return value == 1;
    }

    public static long setByteCache(IBaseBufferRW buf,long bytePos, ByteCache value){
        int numBytes = value.getSize();
        for(int i=0;i<numBytes;++i){
            bytePos = setByte(buf,bytePos,value.getByte(i));
        }
        return bytePos;
    }

    public static ByteCache getByteCache(IBaseBufferRO buf,long bytePos, int numBytes,ByteCache bcRet){
        bcRet.setSize(numBytes);
        for(int i=0;i<numBytes;++i){
            bcRet.setByte(i,getByte(buf,bytePos));
            bytePos += ByteAddressableBufferCls.getByteWriteSizeInBytes();
        }
        return bcRet;
    }

    public static long getBytesFromBuffer(IBaseBufferRO buf,long bytePos, byte numBytes)
    {
        ASSERT.IFTHROW(numBytes > Long.BYTES,"Can only get up to Long.BYTES Bytes");

        return buf.getBytesToLong(bytePos,numBytes);
    }

    public static long setBytesFromBuffer(IBaseBufferRW buf,long bytePos, byte numBytes,long bytesToSet)
    {
        ASSERT.IFTHROW(numBytes > Long.BYTES,"Can only get up to Long.BYTES Bytes");

        return buf.setLongBytes(bytePos,numBytes,bytesToSet);
    }

}
