package buffer.helpers.bit;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.types.ByteCache;
import buffer.types.PString;
import javaHelpers.ASSERT;
import javaHelpers.NumberHelpers;

public class BitAddressableBuffer {
    public static long setBitsAsLong(IBaseBufferRW buf,long bitPos,long numbits, long value){
        return setBitsFromBuffer(buf,bitPos,numbits,value);
    }

    public static long getBitsAsLong(IBaseBufferRO buf,long bitPos,long numbits){
        return getBitsFromBuffer(buf,bitPos, (byte) numbits);
    }

    public static long setLong(IBaseBufferRW buf,long bitPos, long value){
        return setBitsFromBuffer(buf,bitPos,BitAddressableBufferCls.getLongWriteSizeInBits(),value);
    }

    public static long getLong(IBaseBufferRO buf,long bitPos){
        return getBitsFromBuffer(buf,bitPos, (byte) BitAddressableBufferCls.getLongWriteSizeInBits());
    }

    public static long setInteger(IBaseBufferRW buf,long bitPos, int value){
        return setBitsFromBuffer(buf,bitPos,BitAddressableBufferCls.getIntWriteSizeInBits(),value);
    }

    public static int getInteger(IBaseBufferRO buf,long bitPos){
        return (int)getBitsFromBuffer(buf,bitPos, (byte) BitAddressableBufferCls.getIntWriteSizeInBits());
    }

    public static long setShort(IBaseBufferRW buf,long bitPos, short value){
        return setBitsFromBuffer(buf,bitPos,BitAddressableBufferCls.getShortWriteSizeInBits(),value);
    }

    public static short getShort(IBaseBufferRO buf,long bitPos){
        return (short)getBitsFromBuffer(buf,bitPos, (byte) BitAddressableBufferCls.getShortWriteSizeInBits());
    }

    public static long setByte(IBaseBufferRW buf,long bitPos, byte value){
        return setBitsFromBuffer(buf,bitPos,BitAddressableBufferCls.getByteWriteSizeInBits(),value);
    }

    public static byte getByte(IBaseBufferRO buf,long bitPos){
        return (byte)getBitsFromBuffer(buf,bitPos, (byte) BitAddressableBufferCls.getByteWriteSizeInBits());
    }

    public static long setFloat(IBaseBufferRW buf,long bitPos, float value){
        int floatBits = Float.floatToIntBits(value);
        ASSERT.IFTHROW(BitAddressableBufferCls.getFloatWriteSizeInBits() != BitAddressableBufferCls.getIntWriteSizeInBits(),"Float and int are not the same size");
        return setBitsFromBuffer(buf,bitPos,BitAddressableBufferCls.getFloatWriteSizeInBits(), floatBits);
    }

    public static float getFloat(IBaseBufferRO buf,long bitPos){
        ASSERT.IFTHROW(BitAddressableBufferCls.getFloatWriteSizeInBits() != BitAddressableBufferCls.getIntWriteSizeInBits(),"Float and int are not the same size");
        return Float.intBitsToFloat((int)getBitsFromBuffer(buf,bitPos, (byte) BitAddressableBufferCls.getFloatWriteSizeInBits()));
    }
    public static long setDouble(IBaseBufferRW buf,long bitPos, double value){
        long doubleBits = Double.doubleToLongBits(value);
        ASSERT.IFTHROW(BitAddressableBufferCls.getDoubleWriteSizeInBits() != BitAddressableBufferCls.getLongWriteSizeInBits(),"Double and long are not the same size");
        return setBitsFromBuffer(buf,bitPos,BitAddressableBufferCls.getDoubleWriteSizeInBits(), (long) doubleBits);
    }

    public static double getDouble(IBaseBufferRO buf,long bitPos){
        ASSERT.IFTHROW(BitAddressableBufferCls.getDoubleWriteSizeInBits() != BitAddressableBufferCls.getLongWriteSizeInBits(),"Double and long are not the same size");
        long doubleBits = getBitsFromBuffer(buf,bitPos, (byte) BitAddressableBufferCls.getDoubleWriteSizeInBits());
        return Double.longBitsToDouble(doubleBits);
    }

    public static long setBoolean(IBaseBufferRW buf,long bitPos, boolean value){
        if (value) {
            return setBitsFromBuffer(buf, bitPos, BitAddressableBufferCls.getBooleanWriteSizeInBits(), 1);
        }
        return setBitsFromBuffer(buf, bitPos, BitAddressableBufferCls.getBooleanWriteSizeInBits(), 0);
    }

    public static boolean getBoolean(IBaseBufferRO buf,long bitPos){
        long value = getBitsFromBuffer(buf,bitPos, (byte) BitAddressableBufferCls.getBooleanWriteSizeInBits());
        return value == 1;
    }

    public static long setByteArray(IBaseBufferRW buf,long bitPos, byte[] value){
        int numBytes = value.length;
        for(int i=0;i<numBytes;++i){
            bitPos = setByte(buf,bitPos,value[i]);
        }
        return bitPos;
    }

    public static byte[] getByteArray(IBaseBufferRO buf,long bitPos, int numBytes,byte[] retArray){
        for(int i=0;i<numBytes;++i){
            retArray[i] = getByte(buf,bitPos);
            bitPos += BitAddressableBufferCls.getByteWriteSizeInBits();
        }
        return retArray;
    }

    public static long setByteCache(IBaseBufferRW buf,long bitPos, ByteCache value){
        int numBytes = value.getSize();
        for(int i=0;i<numBytes;++i){
            bitPos = setByte(buf,bitPos,value.getByte(i));
        }
        return bitPos;
    }

    public static ByteCache getByteCache(IBaseBufferRO buf,long bitPos, int numBytes,ByteCache bcRet){
        bcRet.setSize(numBytes);
        for(int i=0;i<numBytes;++i){
            bcRet.setByte(i,getByte(buf,bitPos));
            bitPos += BitAddressableBufferCls.getByteWriteSizeInBits();
        }
        return bcRet;
    }

    public static long byteSizeBuffer(long bitSize)
    {
        return NumberHelpers.bitsToBytesNeeded(bitSize);
    }

    public static long getBitsFromBuffer(IBaseBufferRO buf,long bitPosition, byte numberBits)
    {
        ASSERT.IFTHROW(numberBits > Long.SIZE,"Can only get up to Long.SIZE bits");

        long lResult;
        // get the bytePos and remaining bits
        long bytePos = NumberHelpers.bitToIncludedBytePos(bitPosition);
        byte remainderBits = NumberHelpers.remainderBits(bitPosition);

        // number bits in second long
        // number bits in second long
        long numBytesNeeded = NumberHelpers.bitsToBytesNeededWithPos(numberBits,bitPosition);
        byte numBitsNeededInLastByte = NumberHelpers.bitsNeededInLastByte(numberBits,bitPosition);

        // Check if we need to get more or less than one long worth of bytes
        if (numBytesNeeded > Long.BYTES){
            lResult = buf.getBytesToLong(bytePos, (byte) Long.BYTES);
            lResult = NumberHelpers.extractBitsFromLong(lResult,remainderBits,numberBits);

            // get  the second long
            long secondLong = buf.getBytesToLong(bytePos + Long.BYTES, (byte) 1);
            long mask = NumberHelpers.maskLS(numBitsNeededInLastByte);
            secondLong &= mask;
            secondLong = NumberHelpers.shiftMS(secondLong, (byte) (numberBits - numBitsNeededInLastByte));


            lResult |= secondLong;

        }else{
            // find out the number of bytes we need to work with

            lResult = buf.getBytesToLong(bytePos, (byte) numBytesNeeded);
            lResult = NumberHelpers.extractBitsFromLong(lResult,remainderBits,numberBits);
        }

        // mask out top bits that aren't needed
        long mask = NumberHelpers.maskLS(numberBits);
        lResult &= mask;

        return lResult;
    }

    public static long setBitsFromBuffer(IBaseBufferRW buf,long bitPosition,long numberBits,long bitToSet)
    {
        ASSERT.IFTHROW(numberBits > Long.SIZE,"Can only get up to Long.SIZE bits");

        // get the bytePos and remaining bits
        long bytePos = NumberHelpers.bitToIncludedBytePos(bitPosition);
        byte remainderBits = NumberHelpers.remainderBits(bitPosition);

        // number bits in second long
        long numBytesNeeded = NumberHelpers.bitsToBytesNeededWithPos(numberBits,bitPosition);
        byte numBitsNeededInLastByte = NumberHelpers.bitsNeededInLastByte(numberBits,bitPosition);

        // maskLS out the part of bitset not needed
        long mask = NumberHelpers.maskLS((byte) numberBits);
        bitToSet &= mask;

        // Check if we need to get more or less than one long worth of bytes
        if (numBytesNeeded > Long.BYTES){
            //set first long
            long curItem = buf.getBytesToLong(bytePos, (byte) Long.BYTES);
            curItem = NumberHelpers.insertBitsIntoLong(curItem,remainderBits,bitToSet, (byte) numberBits);
            buf.setLongBytes(bytePos, (byte) Long.BYTES,curItem);

            // set second long
            bitToSet = NumberHelpers.shiftLS(bitToSet, (byte) (numberBits - numBitsNeededInLastByte));
            curItem = buf.getBytesToLong(bytePos + Long.BYTES, (byte) 1);

            curItem = NumberHelpers.insertBitsIntoLong(curItem, (byte) 0,bitToSet,numBitsNeededInLastByte);
            buf.setLongBytes((bytePos + Long.BYTES), (byte) 1,curItem);
        }else{
            // find out the number of bytes we need to work with

            long curItem = buf.getBytesToLong(bytePos, (byte) (numBytesNeeded));
            curItem = NumberHelpers.insertBitsIntoLong(curItem,remainderBits,bitToSet, (byte) numberBits);

            buf.setLongBytes(bytePos, (byte) numBytesNeeded,curItem);
        }

        return bitPosition+numberBits;
    }

    public static long setString(IBaseBufferRW buf, long bitPos, String name) {
        byte[] bytes = name.getBytes();
        bitPos = setInteger(buf,bitPos,bytes.length);
        bitPos = setByteArray(buf,bitPos,bytes);
        return bitPos;
    }

    public static String getString(IBaseBufferRO buf, long bitPos) {
        int numBytes = getInteger(buf,bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();
        byte[] bytes = new byte[numBytes];
        getByteArray(buf,bitPos,numBytes,bytes);
        return new String(bytes);
    }
}
