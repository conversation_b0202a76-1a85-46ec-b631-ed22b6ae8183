package buffer.helpers.bit;

public class BitAddressableBufferCls {
    public static long getShortWriteSizeInBits(){
        return Short.SIZE;
    }

    public static long getByteWriteSizeInBits(){
        return Byte.SIZE;
    }

    public static long getIntWriteSizeInBits(){
        return Integer.SIZE;
    }

    public static long getLongWriteSizeInBits(){
        return Long.SIZE;
    }

    public static long getDoubleWriteSizeInBits(){
        return Double.SIZE;
    }

    public static long getFloatWriteSizeInBits(){
        return Float.SIZE;
    }

    public static long getBooleanWriteSizeInBits(){
        return 1;
    }

    public static long getStringWriteSizeInBits(String name) {
        long size = getIntWriteSizeInBits();
        size += (long) name.getBytes().length * Byte.SIZE;

        return size;
    }
}
