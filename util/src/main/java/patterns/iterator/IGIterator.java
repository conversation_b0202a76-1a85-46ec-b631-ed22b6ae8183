package patterns.iterator;

import java.util.Iterator;

public interface IGIterator<OBJ> extends Iterator<OBJ> {
    boolean ASCENDING = true;
    boolean DESCENDING = false;

    void resetIterator();

    void setMinValue(OBJ minKey, boolean bInclusive);
    OBJ getMinValue();
    boolean getMinInclusive();
    void setMaxValue(OBJ maxKey, boolean bInclusive);
    OBJ getMaxValue();
    boolean getMaxInclusive();

    void setAscendingDescending(boolean bAscending);
    boolean getAscendingDescending();

    void setSortOrder(ISortOrder sortOrder);
    ISortOrder getSortOrder();

    boolean isOptimizedSortOrder(ISortOrder sortOrder);

}
