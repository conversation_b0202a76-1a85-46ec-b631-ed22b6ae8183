package patterns.iterator;

public abstract class GIterator<OBJ> implements IGIterator<OBJ>{
    protected OBJ minKey;
    protected boolean minInclusive;
    protected OBJ maxKey;
    protected boolean maxInclusive;

    protected boolean bAscendingDescending;

    protected ISortOrder sortOrder;

    @Override
    public void resetIterator() {
        minKey = null;
        maxKey = null;
        minInclusive = true;
        maxInclusive = true;
        bAscendingDescending = IGIterator.ASCENDING;
        sortOrder = null;
    }

    @Override
    public void setMinValue(OBJ minKey, boolean bInclusive) {
        this.minKey = minKey;
        this.minInclusive = bInclusive;
    }

    @Override
    public OBJ getMinValue() {
        return this.minKey;
    }

    @Override
    public boolean getMinInclusive() {
        return this.minInclusive;
    }

    @Override
    public void setMaxValue(OBJ maxKey, boolean bInclusive) {
        this.maxKey = maxKey;
        this.maxInclusive = bInclusive;
    }

    @Override
    public OBJ getMaxValue() {
        return this.maxKey;
    }

    @Override
    public boolean getMaxInclusive() {
        return this.maxInclusive;
    }

    @Override
    public void setAscendingDescending(boolean bAscending) {
        this.bAscendingDescending = bAscending;
    }

    @Override
    public boolean getAscendingDescending() {
        return false;
    }

    @Override
    public void setSortOrder(ISortOrder sortOrder) {
        this.sortOrder = sortOrder;
    }

    @Override
    public ISortOrder getSortOrder() {
        return this.sortOrder;
    }

    @Override
    public boolean isOptimizedSortOrder(ISortOrder sortOrder) {
        return false;
    }
}
