package thread;

public class ThreadOwned implements IThreadOwned{
    Thread threadOwner;

    public ThreadOwned() {
        this.threadOwner = null;
    }

    @Override
    public Thread getThreadOwner(){
        return threadOwner;
    }

    @Override
    public Thread setThreadOwner(Thread threadOwner){
        Thread curThread;
        curThread = this.threadOwner;
        this.threadOwner = threadOwner;

        return curThread;
    }

    @Override
    public boolean checkUsedByThreadOwner(){
        if (this.threadOwner == null){
            return true;
        }
        return ThreadHelper.getCurrentThread() == this.threadOwner;

    }

}
