package display;

import javaHelpers.ClassHelpers;
import ptype.IPTType;
import ptype.types.PTStruct;

/**
 * Utility class for displaying PTStruct hierarchy in a readable format
 */
public class PTStructHierarchyDisplay {
    
    /**
     * Display PTStruct hierarchy using default options
     * @param struct PTStruct to display
     * @return formatted string representation
     */
    public static String display(PTStruct struct) {
        return display(struct, DisplayOptions.defaultOptions());
    }
    
    /**
     * Display PTStruct hierarchy with custom options
     * @param struct PTStruct to display
     * @param options display formatting options
     * @return formatted string representation
     */
    public static String display(PTStruct struct, DisplayOptions options) {
        if (struct == null) {
            return options.getNullValueDisplay();
        }
        
        StringBuilder sb = new StringBuilder();
        displayStruct(struct, sb, 0, options);
        return sb.toString();
    }
    
    /**
     * Print PTStruct hierarchy to System.out using default options
     * @param struct PTStruct to print
     */
    public static void print(PTStruct struct) {
        System.out.println(display(struct));
    }
    
    /**
     * Print PTStruct hierarchy to System.out with custom options
     * @param struct PTStruct to print
     * @param options display formatting options
     */
    public static void print(PTStruct struct, DisplayOptions options) {
        System.out.println(display(struct, options));
    }
    
    /**
     * Internal method to recursively display struct hierarchy
     * @param struct PTStruct to display
     * @param sb StringBuilder to append to
     * @param depth current depth level
     * @param options display options
     */
    private static void displayStruct(PTStruct struct, StringBuilder sb, int depth, DisplayOptions options) {
        if (depth >= options.getMaxDepth()) {
            appendIndent(sb, depth, options);
            sb.append("... (max depth reached)\n");
            return;
        }
        
        String structTypeName = struct.getClass().getSimpleName();
        if (options.isShowTypes()) {
            appendIndent(sb, depth, options);
            sb.append(structTypeName).append(" {\n");
            depth++;
        }
        
        int numFields = struct.getNumberFields();
        for (int i = 0; i < numFields; i++) {
            PTStruct.FieldEntry field = struct.getField(i);
            if (field == null) {
                continue;
            }
            
            if (field.value == null && !options.isShowNullFields()) {
                continue;
            }
            
            displayField(field, sb, depth, options);
        }
        
        if (options.isShowTypes()) {
            appendIndent(sb, depth - 1, options);
            sb.append("}\n");
        }
    }
    
    /**
     * Display a single field
     * @param field FieldEntry to display
     * @param sb StringBuilder to append to
     * @param depth current depth level
     * @param options display options
     */
    private static void displayField(PTStruct.FieldEntry field, StringBuilder sb, int depth, DisplayOptions options) {
        appendIndent(sb, depth, options);
        sb.append(field.name);
        
        if (options.isShowTypes() && field.value != null) {
            sb.append(" (").append(getTypeDisplayName(field.value)).append(")");
        }
        
        if (options.isShowValues()) {
            sb.append(options.getFieldSeparator());
            
            if (field.value == null) {
                sb.append(options.getNullValueDisplay());
            } else {
                displayValue(field.value, sb, depth, options);
            }
        }
        
        sb.append("\n");
    }
    
    /**
     * Display the value of a field
     * @param value IPTType value to display
     * @param sb StringBuilder to append to
     * @param depth current depth level
     * @param options display options
     */
    private static void displayValue(IPTType value, StringBuilder sb, int depth, DisplayOptions options) {
        // Check if this is a nested struct
        PTStruct nestedStruct = ClassHelpers.getClassAsNull(PTStruct.class, value);
        if (nestedStruct != null) {
            sb.append("\n");
            displayStruct(nestedStruct, sb, depth + 1, options);
            return;
        }
        
        // For primitive types, try to get string representation
        if (value.canRepresentAsString()) {
            String stringValue = value.getAsString();
            sb.append(formatPrimitiveValue(stringValue, value));
        } else {
            sb.append("<unable to display>");
        }
    }
    
    /**
     * Format primitive values with appropriate quotes/formatting
     * @param stringValue string representation of the value
     * @param originalValue original IPTType for type checking
     * @return formatted string
     */
    private static String formatPrimitiveValue(String stringValue, IPTType originalValue) {
        // Add quotes around string values for clarity
        if (originalValue.getClass().getSimpleName().equals("PTString")) {
            return "\"" + stringValue + "\"";
        }
        return stringValue;
    }
    
    /**
     * Get a user-friendly type name for display
     * @param value IPTType to get name for
     * @return display name
     */
    private static String getTypeDisplayName(IPTType value) {
        String className = value.getClass().getSimpleName();
        // Remove "PT" prefix for cleaner display
        if (className.startsWith("PT")) {
            return className.substring(2);
        }
        return className;
    }
    
    /**
     * Append appropriate indentation to StringBuilder
     * @param sb StringBuilder to append to
     * @param depth indentation depth
     * @param options display options containing indent string
     */
    private static void appendIndent(StringBuilder sb, int depth, DisplayOptions options) {
        for (int i = 0; i < depth; i++) {
            sb.append(options.getIndentString());
        }
    }
}
