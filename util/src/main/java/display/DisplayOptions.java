package display;

/**
 * Configuration options for PTStruct hierarchy display formatting
 */
public class DisplayOptions {
    private String indentString = "  ";
    private boolean showTypes = true;
    private boolean showValues = true;
    private boolean showNullFields = false;
    private String fieldSeparator = ": ";
    private String nullValueDisplay = "<null>";
    private int maxDepth = Integer.MAX_VALUE;
    
    public DisplayOptions() {
        // Use default values
    }
    
    /**
     * Create DisplayOptions with custom indent string
     * @param indentString String to use for each level of indentation
     */
    public DisplayOptions(String indentString) {
        this.indentString = indentString;
    }
    
    /**
     * Get the string used for indentation at each level
     * @return indent string
     */
    public String getIndentString() {
        return indentString;
    }
    
    /**
     * Set the string used for indentation at each level
     * @param indentString indent string to use
     * @return this DisplayOptions for method chaining
     */
    public DisplayOptions setIndentString(String indentString) {
        this.indentString = indentString;
        return this;
    }
    
    /**
     * Check if type information should be displayed
     * @return true if types should be shown
     */
    public boolean isShowTypes() {
        return showTypes;
    }
    
    /**
     * Set whether to display type information
     * @param showTypes true to show types
     * @return this DisplayOptions for method chaining
     */
    public DisplayOptions setShowTypes(boolean showTypes) {
        this.showTypes = showTypes;
        return this;
    }
    
    /**
     * Check if field values should be displayed
     * @return true if values should be shown
     */
    public boolean isShowValues() {
        return showValues;
    }
    
    /**
     * Set whether to display field values
     * @param showValues true to show values
     * @return this DisplayOptions for method chaining
     */
    public DisplayOptions setShowValues(boolean showValues) {
        this.showValues = showValues;
        return this;
    }
    
    /**
     * Check if null/unset fields should be displayed
     * @return true if null fields should be shown
     */
    public boolean isShowNullFields() {
        return showNullFields;
    }
    
    /**
     * Set whether to display null/unset fields
     * @param showNullFields true to show null fields
     * @return this DisplayOptions for method chaining
     */
    public DisplayOptions setShowNullFields(boolean showNullFields) {
        this.showNullFields = showNullFields;
        return this;
    }
    
    /**
     * Get the separator string between field name and value
     * @return field separator string
     */
    public String getFieldSeparator() {
        return fieldSeparator;
    }
    
    /**
     * Set the separator string between field name and value
     * @param fieldSeparator separator string
     * @return this DisplayOptions for method chaining
     */
    public DisplayOptions setFieldSeparator(String fieldSeparator) {
        this.fieldSeparator = fieldSeparator;
        return this;
    }
    
    /**
     * Get the string to display for null values
     * @return null value display string
     */
    public String getNullValueDisplay() {
        return nullValueDisplay;
    }
    
    /**
     * Set the string to display for null values
     * @param nullValueDisplay string to show for null values
     * @return this DisplayOptions for method chaining
     */
    public DisplayOptions setNullValueDisplay(String nullValueDisplay) {
        this.nullValueDisplay = nullValueDisplay;
        return this;
    }
    
    /**
     * Get the maximum depth to traverse
     * @return maximum depth
     */
    public int getMaxDepth() {
        return maxDepth;
    }
    
    /**
     * Set the maximum depth to traverse (prevents infinite recursion)
     * @param maxDepth maximum depth to traverse
     * @return this DisplayOptions for method chaining
     */
    public DisplayOptions setMaxDepth(int maxDepth) {
        this.maxDepth = maxDepth;
        return this;
    }
    
    /**
     * Create a default DisplayOptions instance
     * @return default DisplayOptions
     */
    public static DisplayOptions defaultOptions() {
        return new DisplayOptions();
    }
    
    /**
     * Create a compact DisplayOptions instance (minimal formatting)
     * @return compact DisplayOptions
     */
    public static DisplayOptions compactOptions() {
        return new DisplayOptions()
            .setIndentString(" ")
            .setShowTypes(false)
            .setFieldSeparator("=");
    }
    
    /**
     * Create a verbose DisplayOptions instance (maximum information)
     * @return verbose DisplayOptions
     */
    public static DisplayOptions verboseOptions() {
        return new DisplayOptions()
            .setIndentString("    ")
            .setShowTypes(true)
            .setShowValues(true)
            .setShowNullFields(true);
    }
}
