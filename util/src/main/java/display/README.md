# PTStruct Hierarchy Display

This package provides utilities for displaying PTStruct hierarchies in a readable, tree-like format.

## Overview

The `PTStructHierarchyDisplay` class allows you to visualize the structure and contents of `PTStruct` objects, including nested structures, with customizable formatting options.

## Quick Start

### Basic Usage

```java
import display.PTStructHierarchyDisplay;
import ptype.types.PTStruct;

// Display with default formatting
PTStructHierarchyDisplay.print(myStruct);

// Get formatted string
String formatted = PTStructHierarchyDisplay.display(myStruct);
```

### Custom Formatting

```java
import display.DisplayOptions;

// Use predefined options
PTStructHierarchyDisplay.print(myStruct, DisplayOptions.compactOptions());
PTStructHierarchyDisplay.print(myStruct, DisplayOptions.verboseOptions());

// Create custom options
DisplayOptions custom = new DisplayOptions()
    .setIndentString("    ")
    .setFieldSeparator(" → ")
    .setShowTypes(true)
    .setShowNullFields(true);

PTStructHierarchyDisplay.print(myStruct, custom);
```

## Features

### Hierarchical Display
- **Nested Structures**: Automatically detects and displays nested PTStruct objects
- **Indentation**: Uses configurable indentation to show hierarchy levels
- **Type Information**: Shows field types (Integer, String, Boolean, etc.)

### Flexible Formatting
- **Customizable Indentation**: Set custom indent strings (spaces, tabs, symbols)
- **Field Separators**: Configure how field names and values are separated
- **Type Display**: Toggle showing/hiding type information
- **Null Field Handling**: Choose whether to display null/unset fields

### Supported Types
- **PTString**: Displayed with quotes for clarity
- **PTInteger**: Displayed as numeric values
- **PTBoolean**: Displayed as true/false
- **PTFloat/PTDouble**: Displayed as decimal numbers
- **PTStruct**: Recursively displayed as nested structures

## DisplayOptions Configuration

### Basic Options
```java
DisplayOptions options = new DisplayOptions()
    .setIndentString("  ")           // Indentation per level
    .setFieldSeparator(": ")         // Between field name and value
    .setShowTypes(true)              // Show type information
    .setShowValues(true)             // Show field values
    .setShowNullFields(false)        // Show null/unset fields
    .setNullValueDisplay("<null>")   // How to display null values
    .setMaxDepth(Integer.MAX_VALUE); // Maximum nesting depth
```

### Predefined Options
- **`DisplayOptions.defaultOptions()`**: Standard formatting with types and values
- **`DisplayOptions.compactOptions()`**: Minimal formatting, no types
- **`DisplayOptions.verboseOptions()`**: Maximum information including null fields

## Examples

### Simple Structure
```java
PTStruct person = createPersonStruct();
PTStructHierarchyDisplay.print(person);
```

Output:
```
PTStruct {
  id (Integer): 42
  name (String): "John Doe"
  active (Boolean): true
  score (Float): 95.5
}
```

### Nested Structure
```java
PTStruct personWithAddress = createPersonWithAddressStruct();
PTStructHierarchyDisplay.print(personWithAddress);
```

Output:
```
PTStruct {
  name (String): "Jane Smith"
  age (Integer): 30
  address (Struct): 
    PTStruct {
      street (String): "123 Main St"
      city (String): "Anytown"
      zipCode (Integer): 12345
    }
}
```

### Compact Format
```java
PTStructHierarchyDisplay.print(person, DisplayOptions.compactOptions());
```

Output:
```
id=42
name="John Doe"
active=true
score=95.5
```

## Advanced Usage

### Custom Indentation
```java
DisplayOptions treeStyle = new DisplayOptions()
    .setIndentString("│   ")
    .setFieldSeparator(" → ");
```

### Structure Only (No Values)
```java
DisplayOptions structureOnly = DisplayOptions.defaultOptions()
    .setShowValues(false);
```

### Maximum Depth Control
```java
DisplayOptions limitedDepth = DisplayOptions.defaultOptions()
    .setMaxDepth(3); // Prevent deep recursion
```

## Implementation Notes

- **Null Safety**: Handles null PTStruct objects and null field values gracefully
- **Type Detection**: Uses reflection to determine field types for display
- **Memory Efficient**: Uses StringBuilder for string construction
- **Recursion Safe**: Includes maximum depth protection to prevent stack overflow

## Testing

The package includes comprehensive unit tests in `PTStructHierarchyDisplayTest.java` covering:
- Basic display functionality
- Nested structure handling
- All display options
- Edge cases (null values, empty structures)
- Custom formatting scenarios

Run tests with:
```bash
mvn test -Dtest=PTStructHierarchyDisplayTest
```

## Demo

See `PTStructHierarchyDisplayDemo.java` for a complete demonstration of all features and formatting options.
