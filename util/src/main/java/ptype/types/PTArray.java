package ptype.types;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import javaHelpers.ClassHelpers;
import javaHelpers.NumberHelpers;
import ptype.IPTType;
import ptype.def.typedef.PDefArray;

import java.util.ArrayList;
import java.util.List;

public class PTArray extends PTType<PDefArray>{
    private List<IPTType> lstElements;

    public PTArray(PDefArray def) {
        super(def);
        this.lstElements = new ArrayList<>();
    }

    public void appendElement(IPTType element){
        this.lstElements.add(element);
    }

    public int numberElements(){
        return this.lstElements.size();
    }

    public IPTType getElement(int idx){
        return this.lstElements.get(idx);
    }

    @Override
    public int typeCompareTo(IPTType compTo) {
        PTArray compToInst = ClassHelpers.getClassAsNull(PTArray.class,compTo);
        if (compToInst == null){
            return IPTType.COMPARE_MAKES_NO_SENSE;
        }

        int curNumberElements = numberElements();
        int compToNumberElements = compToInst.numberElements();

        long minNumberElements = NumberHelpers.getMinValue(curNumberElements,compToNumberElements);

        for(int i = 0;i<minNumberElements;++i){
            IPTType thisElement = getElement(i);
            IPTType compToElement = compToInst.getElement(i);
            int cmp = thisElement.typeCompareTo(compToElement);
            if (cmp != 0){
                return cmp;
            }
        }

        if (curNumberElements < compToNumberElements){
            return -1;
        }else if (curNumberElements > compToNumberElements){
            return 1;
        }

        return 0;
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos)
    {
        int numberElement = BitAddressableBuffer.getInteger(buf, bitPos);


        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();
        return 0;
    }

    @Override
    public long getWriteSize() {
        long size = BitAddressableBufferCls.getIntWriteSizeInBits();
        size += BitAddressableBufferCls.getIntWriteSizeInBits() * numberElements();
        for(IPTType cur:lstElements){
            size += cur.getWriteSize();
        }
        return size;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos =BitAddressableBuffer.setInteger(buf,bitPos,numberElements());
        int entityPos = (int)(bitPos + (BitAddressableBufferCls.getIntWriteSizeInBits() * numberElements()));
        for(IPTType cur:lstElements){
            bitPos =BitAddressableBuffer.setInteger(buf,bitPos,entityPos);
            entityPos += (int)cur.getWriteSize();
        }
        for(IPTType cur:lstElements){
            bitPos = cur.writeObject(buf,bitPos);
        }
        return bitPos;
    }
}
