package ptype.types;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import javaHelpers.ASSERT;
import ptype.IPTType;
import ptype.def.typedef.PDefBoolean;

public class PTBoolean extends PTType<PDefBoolean> {
    private boolean value;

    public PTBoolean(PDefBoolean def) {
        super(def);
    }

    public PTBoolean(PDefBoolean def, boolean value) {
        super(def);
        this.value = value;
    }

    public PTBoolean(boolean value) {
        super(PDefBoolean.DefaultDef);
        this.value = value;
    }

    boolean getValue(){
        return value;
    }

    void setValue(boolean value){
        this.value = value;
    }

    @Override
    public boolean canRepresentAsType(IPTType fromValue) {
        return fromValue.canRepresentAsBoolean();
    }

    @Override
    public void setFromType(IPTType fromValue) {
        ASSERT.IFTHROW(!fromValue.canRepresentAsBoolean(),"Can not set from type");
        this.value = fromValue.getAsBoolean();
    }

    @Override
    public boolean canRepresentAsBoolean() {
        return true;
    }

    @Override
    public boolean getAsBoolean() {
        return this.value;
    }

    @Override
    public void setFromBoolean(boolean setFrom) {
        this.value = setFrom;
    }

    @Override
    public boolean canRepresentAsInteger() {
        return true;
    }

    @Override
    public long getAsLong() {
        if (this.value){
            return 1;
        }
        return 0;
    }

    @Override
    public void setFromLong(long setFrom) {
        this.value = setFrom == 0;
    }

    @Override
    public boolean canRepresentAsDouble() {
        return true;
    }

    @Override
    public double getAsDouble() {
        if (this.value){
            return 1.0;
        }
        return 0;
    }

    @Override
    public void setFromDouble(double setFrom) {
        this.value = setFrom != 0;
    }

    @Override
    public boolean canRepresentAsString() {
        return true;
    }

    @Override
    public String getAsString() {
        if (this.value){
            return "true";
        }
        return "false";
    }

    @Override
    public void setFromString(String setFrom) {
        this.value = setFrom.compareToIgnoreCase("true") == 0;
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        this.value = BitAddressableBuffer.getBoolean(buf,bitPos);
        return bitPos + getWriteSize();
    }

    @Override
    public long getWriteSize() {
        return BitAddressableBufferCls.getBooleanWriteSizeInBits();
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return BitAddressableBuffer.setBoolean(buf,bitPos,this.value);
    }

    @Override
    public int typeCompareTo(IPTType compTo) {
        if (compTo.canRepresentAsBoolean()){
            boolean bcmpTo = compTo.getAsBoolean();
            if (value == bcmpTo){
                return 0;
            }else if (value){
                return 1;
            }
            return 0;
        }
        ASSERT.THROW("can't compare to type");
        return 0;
    }
}
