package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import ptype.def.typedef.PDefDouble;
import ptype.types.PTDouble;

public class PDefDoubleBuilder extends PDef<PERSON><PERSON>er<PTDouble>{
    @Override
    public PDefDouble Build() {
        return new PDefDouble(this);
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        return bitPos;
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return bitPos;
    }
}
