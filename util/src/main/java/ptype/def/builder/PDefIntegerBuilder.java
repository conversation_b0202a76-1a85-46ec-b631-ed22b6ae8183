package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import javaHelpers.NumberHelpers;
import ptype.def.typedef.PDefInteger;
import ptype.types.PTInteger;

public class PDefIntegerBuilder extends PDefBuilder<PTInteger>{
    private int minValue;
    private int maxValue;

    @Override
    public PDefInteger Build() {
        return new PDefInteger(this,minValue, maxValue); // Using default 64-bit size
    }

    public PDefInteger Build(int numBits) {
        addMaxBits(numBits);
        return new PDefInteger(this,minValue, maxValue); // Using default 64-bit size
    }

    public PDefIntegerBuilder addRange(int minValue, int maxValue) {
        this.minValue = minValue;
        this.maxValue = maxValue;
        return this;
    }

    public PDefIntegerBuilder addMaxBits(int numBits) {
        this.minValue = (int)NumberHelpers.getMinValue(numBits);
        this.maxValue = (int)NumberHelpers.getMaxValue(numBits);
        return this;
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        return bitPos;
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return bitPos;
    }
}
