package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.def.typedef.PDefString;
import ptype.types.PTString;

public class PDefStringBuilder extends PDef<PERSON>uilder<PTString>{

    @Override
    public PDefString Build() {
        return new PDefString(this,PDefString.MAX_UNSPECIFIED_MAX_STRING_SIZE);
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        BitAddressableBuffer.getInteger(buf,bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();
        return bitPos;
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return BitAddressableBufferCls.getIntWriteSizeInBits() ;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,PDefString.MAX_UNSPECIFIED_MAX_STRING_SIZE);
        return bitPos;
    }
}
