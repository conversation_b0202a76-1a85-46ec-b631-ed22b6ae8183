package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.IPTType;

public abstract class PDefBuilder<INST extends IPTType> implements ITypeDefBuilder<INST>
{
    public static PDefBuilder<?> readBuilder(IBaseBufferRO buf, long bitPos) {
        int type = BitAddressableBuffer.getByte(buf,bitPos);
        bitPos += BitAddressableBufferCls.getByteWriteSizeInBits();

        PDefBuilder<?> builder = null;
        switch (type) {
            case 0 -> builder = new PDefBooleanBuilder();
            case 1 -> builder = new PDefIntegerBuilder();
            case 2 -> builder = new PDefFloatBuilder();
            case 3 -> builder = new PDefDoubleBuilder();
            case 4 -> builder =new PDefStringBuilder();
            case 5 -> builder =new PDefArrayBuilder();
            case 6 -> builder =new PDefStructBuilder();
            default -> throw new IllegalStateException("Unexpected value: " + type);
        };

        if (builder != null){
            bitPos = builder.readObject(buf, bitPos);
        }

        return builder;
    }

    public static long getBuilderWriteSize(ITypeDefBuilder<?> builder) {
        return BitAddressableBufferCls.getByteWriteSizeInBits() + builder.getWriteSize();
    }

    public static long writeBuilder(IBaseBufferRW buf, long bitPos, ITypeDefBuilder<?> builder) {
        byte type;

        if (builder instanceof PDefBooleanBuilder) {
            type = 0;
        } else if (builder instanceof PDefIntegerBuilder) {
            type = 1;
        } else if (builder instanceof PDefFloatBuilder) {
            type = 2;
        } else if (builder instanceof PDefDoubleBuilder) {
            type = 3;
        } else if (builder instanceof PDefStringBuilder) {
            type = 4;
        } else if (builder instanceof PDefArrayBuilder) {
            type = 5;
        } else if (builder instanceof PDefStructBuilder) {
            type = 6;
        } else {
            throw new IllegalStateException("Unexpected value: " + builder);
        }

        bitPos = BitAddressableBuffer.setByte(buf,bitPos,type);
        bitPos = builder.writeObject(buf,bitPos);
        return bitPos;
    }
}
