package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.def.typedef.ITypeDef;
import ptype.def.typedef.PDefStruct;
import ptype.def.typedef.StructEntry;
import ptype.def.typedef.TypeDef;
import ptype.types.PTStruct;

import java.util.ArrayList;
import java.util.List;

public class PDefStructBuilder extends PDefBuilder<PTStruct>{
    private List<StructEntry> lstFields;
    private boolean bFixedFields = false;

    public PDefStructBuilder() {
        this.lstFields = new ArrayList<>();
    }

    @Override
    public PDefStruct Build() {
        StructEntry[] orderedEntries = orderFields();
        return new PDefStruct(this,bFixedFields, orderedEntries);
    }
    
    public PDefStructBuilder addField(String name, ITypeDef typeDef, boolean optional) {
        lstFields.add(new StructEntry(name, optional, (TypeDef<?>) typeDef));
        return this;
    }
    
    public PDefStructBuilder setFixedFields(boolean bFixedFields) {
        this.bFixedFields = bFixedFields;
        return this;
    }
    
    public StructEntry[] orderFields() {
        List<StructEntry> nonOptionalFields = new ArrayList<>();
        List<StructEntry> optionalFields = new ArrayList<>();

        // Separate fields by optional status while preserving insertion order
        for(StructEntry entry : lstFields) {
            if (entry.optional) {
                optionalFields.add(entry);
            } else {
                nonOptionalFields.add(entry);
            }
        }

        // Create result array with non-optional fields first, then optional fields
        StructEntry[] orderedFields = new StructEntry[lstFields.size()];
        int index = 0;

        // Add non-optional fields first
        for(StructEntry entry : nonOptionalFields) {
            orderedFields[index++] = entry;
        }

        // Add optional fields second
        for(StructEntry entry : optionalFields) {
            orderedFields[index++] = entry;
        }

        return orderedFields;
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        bFixedFields = BitAddressableBuffer.getBoolean(buf,bitPos);
        bitPos += BitAddressableBufferCls.getBooleanWriteSizeInBits();

        int numEntries = BitAddressableBuffer.getInteger(buf,bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();

        for(int i = 0;i < numEntries;++i){
            StructEntry entry = new StructEntry("",false);
            bitPos = entry.readObject(buf,bitPos);
            lstFields.add(entry);
        }
        return 0;
    }

    @Override
    public boolean isFixedSize() {
        if (!this.bFixedFields){
            return false;
        }

        // see if the types are fixed size
        for (StructEntry cur : this.lstFields) {
            if (cur.optional){
                return false;
            }
            for (ITypeDef<?> type : cur.types) {
                if (!type.isFixedSize()) {
                    return false;
                }
            }
        }

        return true;
    }

    @Override
    public long getWriteSize() {
        long size = BitAddressableBufferCls.getBooleanWriteSizeInBits();

        // number of entries
        size += BitAddressableBufferCls.getIntWriteSizeInBits();

        // write each of the entries
        for (StructEntry cur : this.lstFields) {
            size += cur.getWriteSize();
        }

        return size;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setBoolean(buf,bitPos,this.bFixedFields);
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,this.lstFields.size());
        for (StructEntry cur : this.lstFields) {
            bitPos = cur.writeObject(buf,bitPos);
        }

        return bitPos;
    }
}
