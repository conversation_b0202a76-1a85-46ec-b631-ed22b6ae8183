package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import ptype.def.typedef.PDefFloat;
import ptype.types.PTFloat;

public class PDefFloatBuilder extends PDef<PERSON>uilder<PTFloat>{

    @Override
    public PDefFloat Build() {
        return new PDefFloat(this);
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        return bitPos;
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return bitPos;
    }
}
