package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.def.typedef.PDefBoolean;
import ptype.types.PTBoolean;

public class PDefBooleanBuilder extends PDefBuilder<PTBoolean>{

    @Override
    public PDefBoolean Build() {
        return new PDefBoolean(this);
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        return bitPos;
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return bitPos;
    }
}
