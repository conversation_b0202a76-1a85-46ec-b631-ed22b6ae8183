package ptype.def.builder;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.def.typedef.ITypeDef;
import ptype.def.typedef.PDefArray;
import ptype.types.PTArray;

import java.util.ArrayList;
import java.util.List;

public class PDef<PERSON>rrayBuilder extends PDefBuilder<PTArray> {
    private boolean openArray = false;
    private int minElements = PDefArray.MIN_UNSPECIFIED_MIN_ELEMENTS;
    private int maxElements = PDefArray.MAX_UNSPECIFIED_MAX_ELEMENTS;
    private List<ITypeDef<?>> allowedTypes;

    public PDefArrayBuilder() {
        this.allowedTypes = new ArrayList<>();
    }

    @Override
    public PDefArray Build() {
        ITypeDef<?>[] allowedTypesArray = allowedTypes.toArray(new ITypeDef<?>[0]);
        return new PDefArray(this,openArray, minElements, maxElements, allowedTypesArray);
    }

    /**
     * Sets whether this is an open array (can contain different types)
     * @param openArray true if the array can contain different types
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setOpenArray(boolean openArray) {
        this.openArray = openArray;
        return this;
    }

    /**
     * Sets the minimum number of elements allowed in the array
     * @param minElements minimum number of elements, or MIN_UNSPECIFIED_MIN_ELEMENTS for no minimum
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setMinElements(int minElements) {
        this.minElements = minElements;
        return this;
    }

    /**
     * Sets the maximum number of elements allowed in the array
     * @param maxElements maximum number of elements, or MAX_UNSPECIFIED_MAX_ELEMENTS for no maximum
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setMaxElements(int maxElements) {
        this.maxElements = maxElements;
        return this;
    }

    /**
     * Adds an allowed type for elements in this array
     * @param allowedType the type definition that elements can have
     * @return this builder for method chaining
     */
    public PDefArrayBuilder addAllowedType(ITypeDef<?> allowedType) {
        this.allowedTypes.add(allowedType);
        return this;
    }

    /**
     * Clears all currently allowed types
     * @return this builder for method chaining
     */
    public PDefArrayBuilder clearAllowedTypes() {
        this.allowedTypes.clear();
        return this;
    }

    /**
     * Sets the allowed types, replacing any previously added types
     * @param allowedTypes array of type definitions that elements can have
     * @return this builder for method chaining
     */
    public PDefArrayBuilder setAllowedTypes(ITypeDef<?>... allowedTypes) {
        this.allowedTypes.clear();
        for (ITypeDef<?> type : allowedTypes) {
            this.allowedTypes.add(type);
        }
        return this;
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {

        // read the number of elements
        int numElements = BitAddressableBuffer.getInteger(buf,bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();

        //
        boolean bOpenArray = BitAddressableBuffer.getBoolean(buf,bitPos);
        bitPos += BitAddressableBufferCls.getBooleanWriteSizeInBits();

        // read the min and max elements
        int minElements = BitAddressableBuffer.getInteger(buf,bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();
        int maxElements = BitAddressableBuffer.getInteger(buf,bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();

        // read the type of the array
        for(int i = 0;i < numElements;++i){
            PDefBuilder<?> builder = PDefBuilder.readBuilder(buf,bitPos);
            bitPos += PDefBuilder.getBuilderWriteSize(builder);
            addAllowedType(builder.Build());
        }
        return bitPos;
    }

    @Override
    public boolean isFixedSize() {
        if (maxElements != PDefArray.MAX_UNSPECIFIED_MAX_ELEMENTS || minElements != PDefArray.MIN_UNSPECIFIED_MIN_ELEMENTS){
            return false;
        }
        for (ITypeDef<?> allowedType : this.allowedTypes) {
            if (!allowedType.isFixedSize()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public long getWriteSize() {
        // number of elements
        int size = (int) BitAddressableBufferCls.getIntWriteSizeInBits();
        // open array flag
        size += (int)BitAddressableBufferCls.getBooleanWriteSizeInBits();
        // min and max elements
        size += (int)(BitAddressableBufferCls.getIntWriteSizeInBits() + BitAddressableBufferCls.getIntWriteSizeInBits());

        // size of the allowed types
        for (ITypeDef<?> allowedType : this.allowedTypes) {
            size += (int)PDefBuilder.getBuilderWriteSize(allowedType.getBuilder());
        }
        return size;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,allowedTypes.size());
        bitPos = BitAddressableBuffer.setBoolean(buf,bitPos,openArray);
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,minElements);
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,maxElements);

        for (ITypeDef<?> allowedType : this.allowedTypes) {
            bitPos = PDefBuilder.writeBuilder(buf,bitPos,allowedType.getBuilder());
        }

        return bitPos;
    }
}
