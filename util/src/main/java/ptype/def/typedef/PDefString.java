package ptype.def.typedef;

import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.def.builder.PDefStringBuilder;
import ptype.types.PTString;

public class PDefString extends TypeDef<PTString>{
    public final static int MAX_UNSPECIFIED_MAX_STRING_SIZE = -1;
    int maxSize = MAX_UNSPECIFIED_MAX_STRING_SIZE;
    public static final PDefString DefaultDef = new PDefString(new PDefStringBuilder(),MAX_UNSPECIFIED_MAX_STRING_SIZE);

    public PDefString(PDefStringBuilder builder,int maxSize)
    {
        super(builder);
        this.maxSize = maxSize;
    }

    @Override
    public boolean isFixedSize() {
        return false;
    }

    @Override
    public long getWriteSize() {
        return BitAddressableBufferCls.getIntWriteSizeInBits();
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return BitAddressableBuffer.setInteger(buf,bitPos,this.maxSize);
    }

    @Override
    public PTString createNewType() {
        return new PTString(this);
    }

    @Override
    public boolean isValid(PTString ptString) {
        return true;
    }
}
