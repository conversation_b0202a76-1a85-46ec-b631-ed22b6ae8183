package ptype.def.typedef;

import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import javaHelpers.ASSERT;
import javaHelpers.NumberHelpers;
import ptype.def.builder.PDefIntegerBuilder;
import ptype.types.PTInteger;

public class PDefInteger extends TypeDef<PTInteger>{
    public static final PDefInteger DefaultDef = new PDefInteger(new PDefIntegerBuilder(),Long.MIN_VALUE,Long.MAX_VALUE);
    private final long minValue;
    private final long maxValue;

    public PDefInteger(PDefIntegerBuilder builder,int numBits) {
        super(builder);
        this.maxValue = NumberHelpers.maskLS((byte)(numBits -1));
        this.minValue = -(this.maxValue + 1);
    }
    public PDefInteger( PDefIntegerBuilder builder, long minValue, long maxValue) {
        super(builder);
        this.minValue = minValue;
        this.maxValue = maxValue;
    }

    public byte getNumberBits(){
        return NumberHelpers.getNumberBitsNeeded(this.minValue,this.maxValue);
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        // Size to store Max value
        long size = BitAddressableBufferCls.getLongWriteSizeInBits();
        // Size to store Min value
        size += BitAddressableBufferCls.getLongWriteSizeInBits();
        return size;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setLong(buf,bitPos,this.maxValue);
        bitPos = BitAddressableBuffer.setLong(buf,bitPos,this.minValue);
        return bitPos;
    }

    @Override
    public PTInteger createNewType() {
        return new PTInteger(this);
    }

    @Override
    public boolean isValid(PTInteger ptInteger) {
        return true;
    }


}
