package ptype.def.typedef;

import buffer.IBaseBufferRW;
import ptype.def.builder.ITypeDefBuilder;
import ptype.def.builder.PDefFloatBuilder;
import ptype.types.PTFloat;

public class PDefFloat extends TypeDef<PTFloat>{

    public static final PDefFloat DefaultDef = new PDefFloat(new PDefFloatBuilder());

    public PDefFloat(PDefFloatBuilder builder) {
        super(builder);
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return bitPos;
    }

    @Override
    public PTFloat createNewType() {
        return new PTFloat(this);
    }

    @Override
    public boolean isValid(PTFloat ptFloat) {
        return true;
    }
}
