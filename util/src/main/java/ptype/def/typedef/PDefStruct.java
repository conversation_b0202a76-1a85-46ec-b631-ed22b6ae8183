package ptype.def.typedef;

import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.def.builder.PDefStringBuilder;
import ptype.def.builder.PDefStructBuilder;
import ptype.types.PTStruct;

public class PDef<PERSON>truct extends TypeDef<PTStruct>{
    private final StructEntry[] lstStructEntry;
    private final boolean bFixedFields;

    public PDefStruct(PDefStructBuilder builder, boolean fixedFields, StructEntry ... entries) {
        super(builder);
        this.lstStructEntry = entries;
        this.bFixedFields = fixedFields;
    }

    public StructEntry getType(int idx){
        return this.lstStructEntry[idx];
    }
    public int getNumberEntries(){
        return this.lstStructEntry.length;
    }

    public int getIndex(String name){
        for(int i = 0;i < this.lstStructEntry.length;++i){
            if (this.lstStructEntry[i].name.compareTo(name) == 0)
                return i;
        }
        return -1;
    }

    @Override
    public boolean isFixedSize() {
        if (!this.bFixedFields){
            return false;
        }

        // see if the types are fixed size
        for (StructEntry cur : this.lstStructEntry) {
            if (cur.optional){
                return false;
            }
            for (ITypeDef<?> type : cur.types) {
                if (!type.isFixedSize()) {
                    return false;
                }
            }
        }

        return true;
    }

    @Override
    public long getWriteSize() {
        long size = BitAddressableBufferCls.getBooleanWriteSizeInBits();

        // number of entries
        size += BitAddressableBufferCls.getIntWriteSizeInBits();

        // write each of the entries
        for (StructEntry cur : this.lstStructEntry) {
            size += cur.getWriteSize();
        }

        return size;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setBoolean(buf,bitPos,this.bFixedFields);
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,this.lstStructEntry.length);
        for (StructEntry cur : this.lstStructEntry) {
            bitPos = cur.writeObject(buf,bitPos);
        }

        return bitPos;
    }

    public boolean isFixedFields() {
        return bFixedFields;
    }

    @Override
    public PTStruct createNewType() {
        return new PTStruct(this);
    }

    @Override
    public boolean isValid(PTStruct ptStruct) {
        int numFields = ptStruct.getNumberFields();
        return true;
    }


}
