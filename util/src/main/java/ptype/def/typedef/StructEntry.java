package ptype.def.typedef;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import patterns.persistence.IPersistentObj;
import ptype.def.builder.PDefBuilder;

public class StructEntry implements IPersistentObj {

    public String name;
    public ITypeDef<?>[] types;
    public boolean optional;

    public  StructEntry(String name,  boolean optional,TypeDef<?> ... typeDefs){
        this.name = name;
        this.types = typeDefs;
        this.optional = optional;
    }

    @Override
    public boolean isFixedSize() {
        if (this.optional){
            return false;
        }
        for (ITypeDef<?> type : this.types) {
            if (!type.isFixedSize()) {
                return false;
            }
        }
        return false;
    }

    @Override
    public long getWriteSize() {
        long size = BitAddressableBufferCls.getStringWriteSizeInBits(name);
        size += BitAddressableBufferCls.getBooleanWriteSizeInBits();

        size += BitAddressableBufferCls.getIntWriteSizeInBits();

        for (ITypeDef<?> type : this.types) {
            size += PDefBuilder.getBuilderWriteSize(type.getBuilder());
        }
        return size;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setString(buf,bitPos,name);
        bitPos = BitAddressableBuffer.setBoolean(buf,bitPos,optional);
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,(byte)types.length);
        for (ITypeDef<?> type : this.types) {
            bitPos = PDefBuilder.writeBuilder(buf,bitPos,type.getBuilder());
        }
        return bitPos;
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {
        name = BitAddressableBuffer.getString(buf,bitPos);
        bitPos += BitAddressableBufferCls.getStringWriteSizeInBits(name);

        optional = BitAddressableBuffer.getBoolean(buf,bitPos);
        bitPos += BitAddressableBufferCls.getBooleanWriteSizeInBits();

        int numTypes = BitAddressableBuffer.getInteger(buf,bitPos);
        bitPos += BitAddressableBufferCls.getIntWriteSizeInBits();

        types = new ITypeDef[numTypes];
        for (int i = 0; i < numTypes; ++i) {
            PDefBuilder<?> builder = PDefBuilder.readBuilder(buf,bitPos);
            bitPos += PDefBuilder.getBuilderWriteSize(builder);
            types[i] = builder.Build();
        }

        return bitPos;
    }
}
