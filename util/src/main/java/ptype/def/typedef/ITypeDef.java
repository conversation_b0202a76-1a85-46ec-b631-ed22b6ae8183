package ptype.def.typedef;

import patterns.persistence.IROPersistentObj;
import patterns.persistence.IRWPersistentObj;
import ptype.IPTType;
import ptype.def.builder.ITypeDefBuilder;
import thread.IMMutable;

public interface ITypeDef<INST extends IPTType> extends IMMutable, IRWPersistentObj {
    public boolean  isFixedSize();
    public INST     createNewType();
    public boolean  isValid(INST inst);
    public ITypeDefBuilder<? extends INST> getBuilder();
}
