package ptype.def.typedef;

import buffer.IBaseBufferRW;
import ptype.def.builder.PDefBooleanBuilder;
import ptype.types.PTBoolean;

public class PDefBoolean extends TypeDef<PTBoolean>
{
    public static final PDefBoolean DefaultDef = new PDefBoolean(new PDefBooleanBuilder());

    public PDefBoolean(PDefBooleanBuilder builder) {
        super(builder);
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return bitPos;
    }

    @Override
    public PTBoolean createNewType() {
        return new PTBoolean(this);
    }

    @Override
    public boolean isValid(PTBoolean ptBoolean) {
        return true;
    }
}
