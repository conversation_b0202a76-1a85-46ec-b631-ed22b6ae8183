package ptype.def.typedef;

import buffer.IBaseBufferRW;
import buffer.helpers.bit.BitAddressableBuffer;
import buffer.helpers.bit.BitAddressableBufferCls;
import ptype.def.builder.ITypeDefBuilder;
import ptype.def.builder.PDefArrayBuilder;
import ptype.types.PTArray;

public class PDefArray extends TypeDef<PTArray>{
    private ITypeDef<?> allowedTypes[];
    private boolean openArray;
    private int maxElements;
    private int minElements;
    public static final int MAX_UNSPECIFIED_MAX_ELEMENTS = -1;
    public static final int MIN_UNSPECIFIED_MIN_ELEMENTS = -1;

    public PDefArray(PDefArrayBuilder builder, boolean openArray, int minElements, int maxElements, ITypeDef<?> ... allowedTypes) {
        super(builder);
        this.allowedTypes = allowedTypes;
        this.openArray = openArray;
        this.maxElements = maxElements;
        this.minElements = minElements;
    }
    @Override
    public boolean isFixedSize() {
        if (maxElements != MAX_UNSPECIFIED_MAX_ELEMENTS || minElements != MIN_UNSPECIFIED_MIN_ELEMENTS){
            return false;
        }
        for (ITypeDef<?> allowedType : this.allowedTypes) {
            if (!allowedType.isFixedSize()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public long getWriteSize() {
        // number of elements
        int size = (int)BitAddressableBufferCls.getIntWriteSizeInBits();
        // open array flag
        size += (int)BitAddressableBufferCls.getBooleanWriteSizeInBits();
        // min and max elements
        size += (int)(BitAddressableBufferCls.getIntWriteSizeInBits() + BitAddressableBufferCls.getIntWriteSizeInBits());

        // size of the allowed types
        for (ITypeDef<?> allowedType : this.allowedTypes) {
            size += (int)allowedType.getWriteSize();
        }
        return size;

    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,allowedTypes.length);
        bitPos = BitAddressableBuffer.setBoolean(buf,bitPos,openArray);
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,minElements);
        bitPos = BitAddressableBuffer.setInteger(buf,bitPos,maxElements);

        for (ITypeDef<?> allowedType : this.allowedTypes) {
            bitPos = allowedType.writeObject(buf,bitPos);
        }

        return bitPos;
    }

    @Override
    public PTArray createNewType() {
        return new PTArray(this);
    }

    @Override
    public boolean isValid(PTArray ptArray) {
        return false;
    }
}
