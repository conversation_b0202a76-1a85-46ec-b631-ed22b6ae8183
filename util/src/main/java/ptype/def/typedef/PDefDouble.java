package ptype.def.typedef;

import buffer.IBaseBufferRW;
import ptype.def.builder.ITypeDefBuilder;
import ptype.def.builder.PDefDoubleBuilder;
import ptype.types.PTDouble;

public class PDefDouble extends TypeDef<PTDouble>{
    public static final PDefDouble DefaultDef = new PDefDouble(new PDefDoubleBuilder());

    public PDefDouble(PDefDoubleBuilder builder) {
        super(builder);
    }

    @Override
    public boolean isFixedSize() {
        return true;
    }

    @Override
    public long getWriteSize() {
        return 0;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return bitPos;
    }

    @Override
    public PTDouble createNewType() {
        return new PTDouble(this);
    }

    @Override
    public boolean isValid(PTDouble ptDouble) {
        return true;
    }
}
