package ptype.def.typedef;

import ptype.IPTType;
import ptype.def.builder.ITypeDefBuilder;

public abstract class TypeDef<INST extends IPTType> implements ITypeDef<INST>{
    protected  ITypeDefBuilder<? extends INST> builder = null;

    protected TypeDef(ITypeDefBuilder<? extends INST> builder) {
        this.builder = builder;
    }

    @Override
    public  ITypeDefBuilder<? extends INST> getBuilder() throws ClassCastException {
        return builder;
    }
}
