package javaHelpers;

public class ClassHelpers
{
    public static <CONVTOCLS> CONVTOCLS getClassAsNull(Class<CONVTOCLS> clsTo,Object objFrom){
        CONVTOCLS retValue2 = null;
        if (clsTo.isInstance(objFrom)) {
            @SuppressWarnings("unchecked") CONVTOCLS retValue = (CONVTOCLS) objFrom;
            retValue2 = retValue;
        }
        return retValue2;
    }
    public static <CONVTOCLS> CONVTOCLS getClassAsAssert(Class<CONVTOCLS> clsTo,Object objFrom){
        CONVTOCLS retValue2 = null;
        if (clsTo.isInstance(objFrom)) {
            @SuppressWarnings("unchecked") CONVTOCLS retValue = (CONVTOCLS) objFrom;
            retValue2 = retValue;
        }
        if (retValue2 == null){
            ASSERT.THROW("Object can not be cast to the expected value");
        }
        return retValue2;
    }

    public static <CONVTOCLS> CONVTOCLS castToClass(Object objFrom){
        @SuppressWarnings("unchecked") CONVTOCLS retValue = (CONVTOCLS) objFrom;
        return retValue;
    }

}
