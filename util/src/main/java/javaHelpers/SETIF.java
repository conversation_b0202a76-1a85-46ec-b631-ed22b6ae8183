package javaHelpers;

public class SETIF {
    public static boolean MaxValueBoolean(boolean current,boolean newValue){
        if (current ){
            return true;
        }
        return newValue;
    }

    public static long Max(long current,long newValue){
        return Math.max(current, newValue);
    }

    public static long Min(long current, long newValue){
        return Math.min(current, newValue);
    }

    public static long AddMaxValue(long value,long curvalue,long maxValue){
        long newValue;
        try {
            newValue = Math.addExact(value,curvalue);
        } catch (Exception e) {
            return maxValue;
        }

        if (newValue > maxValue){
            return maxValue;
        }
        return newValue;

    }

    public static int AddMaxValue(int value,int curvalue,int maxValue){
        int newValue;
        try {
            newValue = Math.addExact(value,curvalue);
        } catch (Exception e) {
            return maxValue;
        }

        if (newValue > maxValue){
            return maxValue;
        }
        return newValue;

    }
}
