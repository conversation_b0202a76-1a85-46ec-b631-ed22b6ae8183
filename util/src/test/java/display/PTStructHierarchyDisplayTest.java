package display;

import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.*;

import ptype.def.typedef.*;
import ptype.types.*;
import ptype.def.builder.PDefStructBuilder;

public class PTStructHierarchyDisplayTest {
    
    private PTStruct simpleStruct;
    private PTStruct nestedStruct;
    private PTStruct complexStruct;
    
    @BeforeEach
    void setUp() {
        // Create simple struct with basic types
        PDefStructBuilder simpleBuilder = new PDefStructBuilder();
        PDefStruct simpleDef = simpleBuilder
            .addField("id", PDefInteger.DefaultDef, false)
            .addField("name", PDefString.DefaultDef, false)
            .addField("active", PDefBoolean.DefaultDef, false)
            .addField("score", PDefFloat.DefaultDef, true)
            .Build();
        
        simpleStruct = simpleDef.createNewType();
        simpleStruct.setField("id", new PTInteger(42));
        simpleStruct.setField("name", new PTString("John Doe"));
        simpleStruct.setField("active", new PTBoolean(true));
        simpleStruct.setField("score", new PTFloat(95.5f));
        
        // Create nested struct
        PDefStructBuilder addressBuilder = new PDefStructBuilder();
        PDefStruct addressDef = addressBuilder
            .addField("street", PDefString.DefaultDef, false)
            .addField("city", PDefString.DefaultDef, false)
            .addField("zipCode", PDefInteger.DefaultDef, false)
            .Build();
        
        PTStruct address = addressDef.createNewType();
        address.setField("street", new PTString("123 Main St"));
        address.setField("city", new PTString("Anytown"));
        address.setField("zipCode", new PTInteger(12345));
        
        PDefStructBuilder personBuilder = new PDefStructBuilder();
        PDefStruct personDef = personBuilder
            .addField("name", PDefString.DefaultDef, false)
            .addField("age", PDefInteger.DefaultDef, false)
            .addField("address", addressDef, false)
            .Build();
        
        nestedStruct = personDef.createNewType();
        nestedStruct.setField("name", new PTString("Jane Smith"));
        nestedStruct.setField("age", new PTInteger(30));
        nestedStruct.setField("address", address);
        
        // Create complex struct with optional fields and nulls
        PDefStructBuilder complexBuilder = new PDefStructBuilder();
        PDefStruct complexDef = complexBuilder
            .addField("required", PDefString.DefaultDef, false)
            .addField("optional", PDefString.DefaultDef, true)
            .addField("nullField", PDefInteger.DefaultDef, true)
            .Build();
        
        complexStruct = complexDef.createNewType();
        complexStruct.setField("required", new PTString("present"));
        complexStruct.setField("optional", new PTString("also present"));
        // nullField is intentionally not set
    }
    
    @Test
    @DisplayName("Should display simple struct with default options")
    void testDisplaySimpleStructDefault() {
        String result = PTStructHierarchyDisplay.display(simpleStruct);
        
        assertNotNull(result);
        assertTrue(result.contains("PTStruct {"));
        assertTrue(result.contains("id (Integer): 42"));
        assertTrue(result.contains("name (String): \"John Doe\""));
        assertTrue(result.contains("active (Boolean): true"));
        assertTrue(result.contains("score (Float): 95.5"));
        assertTrue(result.contains("}"));
    }
    
    @Test
    @DisplayName("Should display nested struct hierarchy")
    void testDisplayNestedStruct() {
        String result = PTStructHierarchyDisplay.display(nestedStruct);
        
        assertNotNull(result);
        assertTrue(result.contains("name (String): \"Jane Smith\""));
        assertTrue(result.contains("age (Integer): 30"));
        assertTrue(result.contains("address (Struct):"));
        assertTrue(result.contains("street (String): \"123 Main St\""));
        assertTrue(result.contains("city (String): \"Anytown\""));
        assertTrue(result.contains("zipCode (Integer): 12345"));
    }
    
    @Test
    @DisplayName("Should handle null struct")
    void testDisplayNullStruct() {
        String result = PTStructHierarchyDisplay.display(null);
        assertEquals("<null>", result);
    }
    
    @Test
    @DisplayName("Should respect showTypes option")
    void testDisplayWithoutTypes() {
        DisplayOptions options = DisplayOptions.defaultOptions().setShowTypes(false);
        String result = PTStructHierarchyDisplay.display(simpleStruct, options);
        
        assertNotNull(result);
        assertFalse(result.contains("PTStruct {"));
        assertFalse(result.contains("(Integer)"));
        assertFalse(result.contains("(String)"));
        assertTrue(result.contains("id: 42"));
        assertTrue(result.contains("name: \"John Doe\""));
    }
    
    @Test
    @DisplayName("Should respect showValues option")
    void testDisplayWithoutValues() {
        DisplayOptions options = DisplayOptions.defaultOptions().setShowValues(false);
        String result = PTStructHierarchyDisplay.display(simpleStruct, options);
        
        assertNotNull(result);
        assertTrue(result.contains("id (Integer)"));
        assertTrue(result.contains("name (String)"));
        assertFalse(result.contains(": 42"));
        assertFalse(result.contains(": \"John Doe\""));
    }
    
    @Test
    @DisplayName("Should handle null fields based on showNullFields option")
    void testDisplayNullFields() {
        // Default behavior - don't show null fields
        String resultDefault = PTStructHierarchyDisplay.display(complexStruct);
        assertFalse(resultDefault.contains("nullField"));
        
        // Show null fields
        DisplayOptions showNulls = DisplayOptions.defaultOptions().setShowNullFields(true);
        String resultWithNulls = PTStructHierarchyDisplay.display(complexStruct, showNulls);
        assertTrue(resultWithNulls.contains("nullField"));
        assertTrue(resultWithNulls.contains("<null>"));
    }
    
    @Test
    @DisplayName("Should use custom indentation")
    void testCustomIndentation() {
        DisplayOptions options = DisplayOptions.defaultOptions().setIndentString("    ");
        String result = PTStructHierarchyDisplay.display(nestedStruct, options);
        
        assertNotNull(result);
        assertTrue(result.contains("    name"));
        assertTrue(result.contains("        street"));
    }
    
    @Test
    @DisplayName("Should use custom field separator")
    void testCustomFieldSeparator() {
        DisplayOptions options = DisplayOptions.defaultOptions().setFieldSeparator(" = ");
        String result = PTStructHierarchyDisplay.display(simpleStruct, options);
        
        assertNotNull(result);
        assertTrue(result.contains("id (Integer) = 42"));
        assertTrue(result.contains("name (String) = \"John Doe\""));
    }
    
    @Test
    @DisplayName("Should respect max depth limit")
    void testMaxDepthLimit() {
        DisplayOptions options = DisplayOptions.defaultOptions().setMaxDepth(1);
        String result = PTStructHierarchyDisplay.display(nestedStruct, options);
        
        assertNotNull(result);
        assertTrue(result.contains("... (max depth reached)"));
    }
    
    @Test
    @DisplayName("Should use compact options correctly")
    void testCompactOptions() {
        DisplayOptions options = DisplayOptions.compactOptions();
        String result = PTStructHierarchyDisplay.display(simpleStruct, options);
        
        assertNotNull(result);
        assertFalse(result.contains("(Integer)"));
        assertTrue(result.contains("id=42"));
        assertTrue(result.contains(" ")); // single space indentation
    }
    
    @Test
    @DisplayName("Should use verbose options correctly")
    void testVerboseOptions() {
        DisplayOptions options = DisplayOptions.verboseOptions();
        String result = PTStructHierarchyDisplay.display(complexStruct, options);
        
        assertNotNull(result);
        assertTrue(result.contains("(String)"));
        assertTrue(result.contains("nullField"));
        assertTrue(result.contains("    ")); // four space indentation
    }
    
    @Test
    @DisplayName("Should print to console without errors")
    void testPrintMethods() {
        // These methods should not throw exceptions
        assertDoesNotThrow(() -> PTStructHierarchyDisplay.print(simpleStruct));
        assertDoesNotThrow(() -> PTStructHierarchyDisplay.print(nestedStruct, DisplayOptions.compactOptions()));
    }
}
