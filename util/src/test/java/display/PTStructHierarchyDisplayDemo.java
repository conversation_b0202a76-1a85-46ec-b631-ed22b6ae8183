package display;

import ptype.def.typedef.*;
import ptype.types.*;
import ptype.def.builder.PDefStructBuilder;

/**
 * Demonstration of PTStruct hierarchy display functionality
 */
public class PTStructHierarchyDisplayDemo {
    
    public static void main(String[] args) {
        System.out.println("=== PTStruct Hierarchy Display Demo ===\n");
        
        // Create a complex nested structure
        PTStruct demoStruct = createDemoStruct();
        
        // Demo 1: Default display
        System.out.println("1. Default Display:");
        PTStructHierarchyDisplay.print(demoStruct);
        
        // Demo 2: Compact display
        System.out.println("\n2. Compact Display:");
        PTStructHierarchyDisplay.print(demoStruct, DisplayOptions.compactOptions());
        
        // Demo 3: Verbose display with null fields
        System.out.println("\n3. Verbose Display (with null fields):");
        PTStructHierarchyDisplay.print(demoStruct, DisplayOptions.verboseOptions());
        
        // Demo 4: Custom formatting
        System.out.println("\n4. Custom Formatting:");
        DisplayOptions customOptions = new DisplayOptions()
            .setIndentString("│   ")
            .setFieldSeparator(" → ")
            .setShowTypes(true)
            .setShowValues(true);
        PTStructHierarchyDisplay.print(demoStruct, customOptions);
        
        // Demo 5: Structure only (no values)
        System.out.println("\n5. Structure Only (no values):");
        DisplayOptions structureOnly = DisplayOptions.defaultOptions()
            .setShowValues(false);
        PTStructHierarchyDisplay.print(demoStruct, structureOnly);
    }
    
    private static PTStruct createDemoStruct() {
        // Create Address struct
        PDefStructBuilder addressBuilder = new PDefStructBuilder();
        PDefStruct addressDef = addressBuilder
            .addField("street", PDefString.DefaultDef, false)
            .addField("city", PDefString.DefaultDef, false)
            .addField("state", PDefString.DefaultDef, false)
            .addField("zipCode", PDefInteger.DefaultDef, false)
            .addField("country", PDefString.DefaultDef, true)
            .Build();
        
        PTStruct address = addressDef.createNewType();
        address.setField("street", new PTString("123 Main Street"));
        address.setField("city", new PTString("Springfield"));
        address.setField("state", new PTString("IL"));
        address.setField("zipCode", new PTInteger(62701));
        address.setField("country", new PTString("USA"));
        
        // Create Contact struct
        PDefStructBuilder contactBuilder = new PDefStructBuilder();
        PDefStruct contactDef = contactBuilder
            .addField("email", PDefString.DefaultDef, false)
            .addField("phone", PDefString.DefaultDef, true)
            .addField("fax", PDefString.DefaultDef, true)
            .Build();
        
        PTStruct contact = contactDef.createNewType();
        contact.setField("email", new PTString("<EMAIL>"));
        contact.setField("phone", new PTString("************"));
        // fax is intentionally left null
        
        // Create Person struct
        PDefStructBuilder personBuilder = new PDefStructBuilder();
        PDefStruct personDef = personBuilder
            .addField("id", PDefInteger.DefaultDef, false)
            .addField("firstName", PDefString.DefaultDef, false)
            .addField("lastName", PDefString.DefaultDef, false)
            .addField("age", PDefInteger.DefaultDef, false)
            .addField("salary", PDefDouble.DefaultDef, true)
            .addField("isActive", PDefBoolean.DefaultDef, false)
            .addField("rating", PDefFloat.DefaultDef, true)
            .addField("address", addressDef, false)
            .addField("contact", contactDef, false)
            .addField("emergencyContact", contactDef, true)
            .Build();
        
        PTStruct person = personDef.createNewType();
        person.setField("id", new PTInteger(12345));
        person.setField("firstName", new PTString("John"));
        person.setField("lastName", new PTString("Doe"));
        person.setField("age", new PTInteger(35));
        person.setField("salary", new PTDouble(75000.50));
        person.setField("isActive", new PTBoolean(true));
        person.setField("rating", new PTFloat(4.8f));
        person.setField("address", address);
        person.setField("contact", contact);
        // emergencyContact is intentionally left null
        
        return person;
    }
}
