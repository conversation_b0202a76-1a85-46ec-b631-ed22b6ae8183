package file.path;

import file.SfDirectory;
import file.name.SfDirectoryName;
import file.name.SfFileName;
import org.junit.jupiter.api.Test;

import java.util.List;

class SfLocationTest {

    @Test
    void getUserLocation() {
        SfDirectory dirExeLauchLocation = SfDirectory.getUserLocation();
        System.out.println(dirExeLauchLocation.dump(1));
    }

    @Test
    void getTmpLocation() {
        SfDirectory dirTmpLocation = SfDirectory.getTmpLocation();
        System.out.println(dirTmpLocation.dump(1));
    }

    @Test
    void getJavaHomeLocation() {
        SfDirectory dirExecutableLocation = SfDirectory.getJavaHomeLocation();
        System.out.println(dirExecutableLocation.dump(1));
    }

    @Test
    void getProgramLocation() {
        SfDirectory dirProgramLocation = SfDirectory.getProgramLocation();
        System.out.println(dirProgramLocation.dump(1));
    }

    @Test
    void getUserHomeDirectory() {
        SfDirectory dirUserHome = SfDirectory.getUserHomeDirectory();
        System.out.println(dirUserHome.dump(1));
    }

    @Test
    void getDesktopDirectory() {
        SfDirectory dirDesktop = SfDirectory.getDesktopDirectory();
        System.out.println(dirDesktop.dump(1));
    }

    @Test
    void getDirectories() {
        SfDirectory currentDir = SfDirectory.getUserLocation();
        System.out.println("Testing getDirectories() for: " + currentDir.dump(1));

        List<SfDirectoryName> directories = currentDir.getDirectories();
        System.out.println("Found " + directories.size() + " directories:");

        for (SfDirectoryName dirName : directories) {
            System.out.println("  - " + dirName.getSFName());
        }
    }

    @Test
    void getFiles() {
        SfDirectory currentDir = SfDirectory.getUserLocation();
        System.out.println("Testing getFiles() for: " + currentDir.dump(1));

        List<SfFileName> files = currentDir.getFiles();
        System.out.println("Found " + files.size() + " files:");

        for (SfFileName fileName : files) {
            System.out.println("  - " + fileName.getSFName());
        }
    }
}