package file.path;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;
import java.io.IOException;

public class SfLocationRelativeTest {

    @Test
    void testGetRelativeLocationBasic() throws IOException {
        // Create a location for /home/<USER>/Documents/proj
        SfLocation location = new SfLocation("/home/<USER>/Documents/proj");
        
        // Go up 1 level should give us /home/<USER>/Documents
        SfLocation parent = location.getRelativeLocation(1);
        String parentPath = parent.getPath();
        assertTrue(parentPath.endsWith("Documents"), "Expected path to end with Documents, got: " + parentPath);
        
        // Go up 2 levels should give us /home/<USER>
        SfLocation grandParent = location.getRelativeLocation(2);
        String grandParentPath = grandParent.getPath();
        assertTrue(grandParentPath.endsWith("user"), "Expected path to end with user, got: " + grandParentPath);
    }

    @Test
    void testGetRelativeLocationZeroLevels() throws IOException {
        SfLocation location = new SfLocation("/home/<USER>/Documents");
        
        // Going up 0 levels should return a copy of the same location
        SfLocation same = location.getRelativeLocation(0);
        assertEquals(location.getPath(), same.getPath());
        
        // But it should be a different object (immutable copy)
        assertNotSame(location, same);
    }

    @Test
    void testGetRelativeLocationInvalidArguments() throws IOException {
        SfLocation location = new SfLocation("/home/<USER>");
        
        // Negative levels should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            location.getRelativeLocation(-1);
        });
        
        // Going up more levels than available should throw exception
        assertThrows(IllegalArgumentException.class, () -> {
            location.getRelativeLocation(10); // More than the path depth
        });
    }

    @Test
    void testImmutability() throws IOException {
        SfLocation original = new SfLocation("/home/<USER>/Documents/proj");
        String originalPath = original.getPath();
        
        // Creating relative locations should not modify the original
        SfLocation parent = original.getRelativeLocation(1);
        SfLocation grandParent = original.getRelativeLocation(2);
        
        // Original should remain unchanged
        assertEquals(originalPath, original.getPath());
        
        // Each relative location should be different
        assertNotEquals(original.getPath(), parent.getPath());
        assertNotEquals(original.getPath(), grandParent.getPath());
        assertNotEquals(parent.getPath(), grandParent.getPath());
    }
}
