package file;

import file.name.SfFileName;
import org.junit.jupiter.api.Test;

import java.io.File;
import java.io.IOException;

import static org.junit.jupiter.api.Assertions.*;

class SfFileTest {

    @Test
    void existsForExistingFile() throws IOException {
        // Test with pom.xml which should exist in the current directory
        SfDirectory currentDir = SfDirectory.getUserLocation();
        SfFileName pomFileName = new SfFileName("pom.xml");
        SfFile pomFile = new SfFile(currentDir, pomFileName);
        
        System.out.println("Testing exists() for pom.xml in: " + currentDir.dump(1));
        boolean exists = pomFile.exists();
        System.out.println("pom.xml exists: " + exists);
        
        // This should be true since pom.xml exists in the project root
        assertTrue(exists, "pom.xml should exist in the current directory");
    }

    @Test
    void existsForNonExistingFile() throws IOException {
        // Test with a file that doesn't exist
        SfDirectory currentDir = SfDirectory.getUserLocation();
        SfFileName nonExistentFileName = new SfFileName("non-existent-file.txt");
        SfFile nonExistentFile = new SfFile(currentDir, nonExistentFileName);
        
        System.out.println("Testing exists() for non-existent-file.txt in: " + currentDir.dump(1));
        boolean exists = nonExistentFile.exists();
        System.out.println("non-existent-file.txt exists: " + exists);
        
        // This should be false since the file doesn't exist
        assertFalse(exists, "non-existent-file.txt should not exist");
    }

    @Test
    void existsForDirectory() throws IOException {
        // Test with a directory name (should return false since exists() checks for files only)
        SfDirectory currentDir = SfDirectory.getUserLocation();
        SfFileName dirAsFileName = new SfFileName("src"); // src is a directory, not a file
        SfFile dirAsFile = new SfFile(currentDir, dirAsFileName);
        
        System.out.println("Testing exists() for 'src' (directory) in: " + currentDir.dump(1));
        boolean exists = dirAsFile.exists();
        System.out.println("'src' exists as file: " + exists);
        
        // This should be false since 'src' is a directory, not a file
        assertFalse(exists, "'src' should not exist as a file (it's a directory)");
    }
}
