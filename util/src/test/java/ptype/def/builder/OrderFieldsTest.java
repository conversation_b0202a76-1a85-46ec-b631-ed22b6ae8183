package ptype.def.builder;

import java.util.ArrayList;
import java.util.List;

/**
 * Standalone test for the orderFields method logic
 * This test doesn't depend on the problematic PTStruct class
 */
public class OrderFieldsTest {
    
    public static void main(String[] args) {
        testOrderFields();
        System.out.println("All tests passed!");
    }
    
    public static void testOrderFields() {
        // Create a mock implementation to test the ordering logic
        List<MockStructEntry> lstFields = new ArrayList<>();
        
        // Add fields in mixed order: optional, required, optional, required
        lstFields.add(new MockStructEntry("optionalField1", null, true));
        lstFields.add(new MockStructEntry("requiredField1", null, false));
        lstFields.add(new MockStructEntry("optionalField2", null, true));
        lstFields.add(new MockStructEntry("requiredField2", null, false));
        
        // Apply the same ordering logic as in PDefStructBuilder
        MockStructEntry[] orderedFields = orderFields(lstFields);
        
        // Verify the results
        assert orderedFields.length == 4 : "Should have 4 fields total";
        
        // First two should be non-optional (required) in insertion order
        assert !orderedFields[0].optional : "First field should be non-optional";
        assert orderedFields[0].name.equals("requiredField1") : "First field should be requiredField1";
        assert !orderedFields[1].optional : "Second field should be non-optional";
        assert orderedFields[1].name.equals("requiredField2") : "Second field should be requiredField2";
        
        // Last two should be optional in insertion order
        assert orderedFields[2].optional : "Third field should be optional";
        assert orderedFields[2].name.equals("optionalField1") : "Third field should be optionalField1";
        assert orderedFields[3].optional : "Fourth field should be optional";
        assert orderedFields[3].name.equals("optionalField2") : "Fourth field should be optionalField2";
        
        System.out.println("Order test passed: " + 
            orderedFields[0].name + "(req), " + 
            orderedFields[1].name + "(req), " + 
            orderedFields[2].name + "(opt), " + 
            orderedFields[3].name + "(opt)");
    }
    
    // Mock implementation of the orderFields logic
    public static MockStructEntry[] orderFields(List<MockStructEntry> lstFields) {
        List<MockStructEntry> nonOptionalFields = new ArrayList<>();
        List<MockStructEntry> optionalFields = new ArrayList<>();
        
        // Separate fields by optional status while preserving insertion order
        for(MockStructEntry entry : lstFields) {
            if (entry.optional) {
                optionalFields.add(entry);
            } else {
                nonOptionalFields.add(entry);
            }
        }
        
        // Create result array with non-optional fields first, then optional fields
        MockStructEntry[] orderedFields = new MockStructEntry[lstFields.size()];
        int index = 0;
        
        // Add non-optional fields first
        for(MockStructEntry entry : nonOptionalFields) {
            orderedFields[index++] = entry;
        }
        
        // Add optional fields second
        for(MockStructEntry entry : optionalFields) {
            orderedFields[index++] = entry;
        }
        
        return orderedFields;
    }
    
    // Mock StructEntry for testing
    static class MockStructEntry {
        public String name;
        public Object type; // Using Object instead of ITypeDef to avoid dependencies
        public boolean optional;

        public MockStructEntry(String name, Object type, boolean optional) {
            this.name = name;
            this.type = type;
            this.optional = optional;
        }
    }
}
