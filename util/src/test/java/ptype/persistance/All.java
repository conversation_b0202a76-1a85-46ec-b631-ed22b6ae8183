package ptype.persistance;

import org.junit.platform.suite.api.Suite;
import org.junit.platform.suite.api.SuiteDisplayName;
import org.junit.platform.suite.api.SelectClasses;

/**
 * JUnit Test Suite for all PTType persistence tests.
 * 
 * This suite runs all individual test classes in the ptype.persistance package:
 * - PTPersistBooleanTest: Tests for PTBoolean persistence operations
 * - PTPersistDoubleTest: Tests for PTDouble persistence operations
 * - PTPersistFloatTest: Tests for PTFloat persistence operations
 * - PTPersistIntegerTest: Tests for PTInteger persistence operations
 * - PTPersistStringTest: Tests for PTString persistence operations
 * 
 * Each test class verifies that PTType objects can be correctly written to
 * and read from buffers, ensuring data integrity across persistence operations.
 * The tests cover:
 * - Basic write/read operations
 * - Multiple writes at different buffer positions
 * - Persistence using type definitions from builders
 * 
 * Usage:
 * Run this test suite to execute all persistence PTType tests in one go.
 * Individual test classes can still be run separately if needed.
 * 
 * To run this suite:
 * - From IDE: Right-click on this class and select "Run All.java"
 * - From command line: Use your build tool (<PERSON><PERSON>/<PERSON>radle) to run this test class
 * 
 * Note: These tests use LocalHeapBaseBufferRW for buffer operations and verify
 * that data can be persisted and retrieved correctly for all supported PTType variants.
 */
@Suite
@SuiteDisplayName("All PTType Persistence Tests")
@SelectClasses({
    PTPersistBooleanTest.class,
    PTPersistDoubleTest.class,
    PTPersistFloatTest.class,
    PTPersistIntegerTest.class,
    PTPersistStringTest.class
})
public class All {
    // This class serves as a test suite runner.
    // No additional code is needed - the @Suite annotations handle everything.
}
