package ptype.persistance;

import buffer.mem.localheap.LocalHeapBaseBufferRW;
import org.junit.jupiter.api.Test;
import ptype.def.builder.PDefArrayBuilder;
import ptype.def.typedef.PDefArray;
import ptype.def.typedef.PDefString;
import ptype.types.PTArray;

public class PTPersistArrayTest
{
    @Test
    void SimpleArrayPersistTest()
    {
        PDefArray arrType = new PDefArrayBuilder().addAllowedType(PDefString.DefaultDef).Build();

        PTArray ptArray = arrType.createNewType();;
        ptArray.appendElement(new ptype.types.PTString("Hello"));
        ptArray.appendElement(new ptype.types.PTString("World"));
        ptArray.appendElement(new ptype.types.PTString("Test"));

        buffer.IBaseBufferRW buffer = new LocalHeapBaseBufferRW(1000);
        ptArray.writeObject(buffer,0L);

        PTArray ptArray2 = arrType.createNewType();
        ptArray2.readObject(buffer,0L);

        assert(ptArray2.numberElements() == 3);
        assert(((ptype.types.PTString)ptArray2.getElement(0)).getAsString().equals("Hello"));
        assert(((ptype.types.PTString)ptArray2.getElement(1)).getAsString().equals("World"));
        assert(((ptype.types.PTString)ptArray2.getElement(2)).getAsString().equals("Test"));
    }
}
