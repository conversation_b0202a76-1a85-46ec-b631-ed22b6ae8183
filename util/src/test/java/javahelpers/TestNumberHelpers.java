package javahelpers;

import javaHelpers.NumberHelpers;
import org.junit.jupiter.api.Test;

public class TestNumberHelpers 
{
    @Test
    public void testAlign()
    {
        for(int i=0;i< 500;++i) {
            int numItems = NumberHelpers.numItemsNeededToByteAlign(i);
            int x = 5;

            long alignSize = NumberHelpers.findNumberItemsToByteAlign(i, 100);
            int y = 5;
        }
    }
}
