package lists;

import org.junit.jupiter.api.Test;

public class BitOperations {

    @Test
    void TestBitOp() {
        long value = 0b111101111;
        int size = 9; // 9 bits
        int offset = 1;

        long offsetvalue = value << Long.SIZE - size; 
        
        //get the first byte;
        long l = offsetvalue >>> (6 * 8 + offset);
        byte setion1= (byte)l;

        int x = 5;

        byte orMask1 = getOrMask(value,1,1,9);
        byte orMask0 = getOrMask(value,0,1,9);
        orMask1 = getZeroMask(value,1,1,9);
        orMask0 = getZeroMask(value,0,1,9);

        orMask1 = getOrMask(value,1,2,9);
        orMask0 = getOrMask(value,0,2,9);

        orMask1 = getZeroMask(value,1,2,9);
        orMask0 = getZeroMask(value,0,2,9);

        long value2 = 0xfefd_fcfb_faf9_f8f7L;
        orMask1 = getOrMask(value2,8,0,Long.SIZE);
        orMask1 = getOrMask(value2,7,0,Long.SIZE);
        orMask1 = getOrMask(value2,6,0,Long.SIZE);
        orMask1 = getOrMask(value2,5,0,Long.SIZE);
        orMask1 = getOrMask(value2,4,0,Long.SIZE);
        orMask1 = getOrMask(value2,3,0,Long.SIZE);
        orMask1 = getOrMask(value2,2,0,Long.SIZE);
        orMask1 = getOrMask(value2,1,0,Long.SIZE);
        orMask1 = getOrMask(value2,0,0,Long.SIZE);

        orMask1 = getZeroMask(value2,8,0,Long.SIZE);
        orMask1 = getZeroMask(value2,7,0,Long.SIZE);
        orMask1 = getZeroMask(value2,6,0,Long.SIZE);
        orMask1 = getZeroMask(value2,5,0,Long.SIZE);
        orMask1 = getZeroMask(value2,4,0,Long.SIZE);
        orMask1 = getZeroMask(value2,3,0,Long.SIZE);
        orMask1 = getZeroMask(value2,2,0,Long.SIZE);
        orMask1 = getZeroMask(value2,1,0,Long.SIZE);
        orMask1 = getZeroMask(value2,0,0,Long.SIZE);

        orMask1 = getOrMask(value2,8,1,Long.SIZE);
        orMask1 = getOrMask(value2,7,1,Long.SIZE);
        orMask1 = getOrMask(value2,6,1,Long.SIZE);
        orMask1 = getOrMask(value2,5,1,Long.SIZE);
        orMask1 = getOrMask(value2,4,1,Long.SIZE);
        orMask1 = getOrMask(value2,3,1,Long.SIZE);
        orMask1 = getOrMask(value2,2,1,Long.SIZE);
        orMask1 = getOrMask(value2,1,1,Long.SIZE);
        orMask1 = getOrMask(value2,0,1,Long.SIZE);

        orMask1 = getZeroMask(value2,8,1,Long.SIZE);
        orMask1 = getZeroMask(value2,7,1,Long.SIZE);
        orMask1 = getZeroMask(value2,6,1,Long.SIZE);
        orMask1 = getZeroMask(value2,5,1,Long.SIZE);
        orMask1 = getZeroMask(value2,4,1,Long.SIZE);
        orMask1 = getZeroMask(value2,3,1,Long.SIZE);
        orMask1 = getZeroMask(value2,2,1,Long.SIZE);
        orMask1 = getZeroMask(value2,1,1,Long.SIZE);
        orMask1 = getZeroMask(value2,0,1,Long.SIZE);
    }

    private byte getOrMask(long value,int section, int offset,int size)
    {
        // may have to do something different with the last section
        if (section == 0){
            long offsetvalue = value << Byte.SIZE;
            long sectionValue = offsetvalue >>> offset;
            return (byte)sectionValue;
        }

        long offsetvalue = value >>> offset;
        long sectionValue = offsetvalue >>> ((section -1) * Long.BYTES);
        /*long offsetvalue = value << Long.SIZE -size;

        //
        section = 8 - section;
        long sectionValue = offsetvalue >>> ((section) * Long.BYTES) + offset;*/

        return (byte)sectionValue;
    }

    private byte getZeroMask(long value,int section, int offset, int size)
    {
        if (section == 0){
            value = 0xffff_ffff_ffff_ffffL;
            long sectionValue = value >>> ((Long.SIZE - Byte.SIZE) + offset) ;
            return (byte)sectionValue;
        }
        value = 0xffff_ffff_ffff_ffffL;
        long offsetvalue =0;
        if(size - offset != 64){
            offsetvalue = value << size - offset;
        }
        long sectionValue = offsetvalue >> ((section-1) * Long.BYTES);


        //
        /*section = 8 - section;
        long sectionValue = offsetvalue >> ((section) * Long.BYTES) + offset;*/

        return (byte)sectionValue;
    }


}
