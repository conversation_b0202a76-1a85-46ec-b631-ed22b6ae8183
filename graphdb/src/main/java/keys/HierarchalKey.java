package keys;

import patterns.comparable.IComparable;

import java.util.ArrayList;
import java.util.List;

public class HierarchalKey
{
    private List<IComparable> m_keys;

    public HierarchalKey() {
        m_keys = new ArrayList<>();
    }

    public void addKey(IComparable key) {
        m_keys.add(key);
    }

    public int compareTo(HierarchalKey other) {
        int size = Math.min(m_keys.size(), other.m_keys.size());
        for (int i = 0; i < size; i++) {
            int cmp = m_keys.get(i).compareTo(other.m_keys.get(i));
            if (cmp != 0) {
                return cmp;
            }
        }
        return Integer.compare(m_keys.size(), other.m_keys.size());
    }
}
