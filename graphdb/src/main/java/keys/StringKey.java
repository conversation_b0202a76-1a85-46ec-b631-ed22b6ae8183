package keys;

import buffer.IBaseBufferRO;
import buffer.IBaseBufferRW;
import javaHelpers.ClassHelpers;
import patterns.comparable.IComparable;
import buffer.helpers.bit.BitAddressableBuffer;

public class String<PERSON>ey implements IKey
{
    private String m_key;

    public StringKey(String key) {
        m_key = key;
    }

    @Override
    public int compareTo(Object o) {
        String other = null;

        // compare to other StringKey
        StringKey otherObj = ClassHelpers.getClassAsNull(StringKey.class,o);
        if (otherObj != null){
            other = otherObj.getKey();
        }

        // compare to string
        if (other == null){
            other = ClassHelpers.getClassAsNull(String.class,o);
        }

        // if not a String or StringKey then not comparable
        if (other == null){
            return IComparable.COMPARE_MAKES_NO_SENSE;
        }

        return m_key.compareTo(other);
    }

    @Override
    public String dump(int detailLevel) {
        return m_key;
    }

    @Override
    public long getHash() {
        return m_key.hashCode();
    }

    @Override
    public long readObject(IBaseBufferRO buf, long bitPos) {

        return 0;
    }

    @Override
    public boolean isFixedSize() {
        return false;
    }

    @Override
    public long getWriteSize() {
        return (long)(m_key.getBytes().length + Integer.BYTES) * Byte.SIZE;
    }

    @Override
    public long writeObject(IBaseBufferRW buf, long bitPos) {
        return 0;
    }

    public String getKey(){
        return m_key;
    }
}
